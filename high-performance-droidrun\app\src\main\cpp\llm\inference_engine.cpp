#include "inference_engine.h"
#include <android/log.h>
#include <memory>
#include <chrono>
#include <thread>

#define LOG_TAG "DroidRunHP"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

namespace droidrun {
namespace llm {

InferenceEngine::InferenceEngine() 
    : initialized_(false)
    , model_handle_(nullptr)
    , context_length_(4096)
    , max_tokens_(512)
    , temperature_(0.2f)
    , threads_(std::thread::hardware_concurrency()) {
}

InferenceEngine::~InferenceEngine() {
    Release();
}

bool InferenceEngine::Initialize(const std::string& model_path, 
                                int context_length, 
                                int threads) {
    if (initialized_) {
        LOGI("推理引擎已经初始化");
        return true;
    }
    
    LOGI("开始初始化推理引擎: %s", model_path.c_str());
    
    try {
        context_length_ = context_length;
        threads_ = threads;
        
        // 根据模型文件扩展名选择推理后端
        if (model_path.find(".gguf") != std::string::npos || 
            model_path.find(".ggml") != std::string::npos) {
            // 使用 llama.cpp 后端
            if (!InitializeLlamaCpp(model_path)) {
                LOGE("初始化 llama.cpp 后端失败");
                return false;
            }
            backend_type_ = BackendType::LLAMA_CPP;
        } else if (model_path.find(".onnx") != std::string::npos) {
            // 使用 ONNX Runtime 后端
            if (!InitializeOnnxRuntime(model_path)) {
                LOGE("初始化 ONNX Runtime 后端失败");
                return false;
            }
            backend_type_ = BackendType::ONNX_RUNTIME;
        } else {
            LOGE("不支持的模型格式: %s", model_path.c_str());
            return false;
        }
        
        // 预热模型
        WarmupModel();
        
        initialized_ = true;
        LOGI("推理引擎初始化成功");
        return true;
        
    } catch (const std::exception& e) {
        LOGE("推理引擎初始化异常: %s", e.what());
        return false;
    }
}

std::string InferenceEngine::Inference(const std::string& prompt, int max_tokens) {
    if (!initialized_) {
        LOGE("推理引擎未初始化");
        return "";
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::string result;
    try {
        switch (backend_type_) {
            case BackendType::LLAMA_CPP:
                result = InferenceWithLlamaCpp(prompt, max_tokens);
                break;
            case BackendType::ONNX_RUNTIME:
                result = InferenceWithOnnxRuntime(prompt, max_tokens);
                break;
            default:
                LOGE("未知的推理后端类型");
                return "";
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        LOGD("推理完成，耗时: %ldms, 输出长度: %zu", duration, result.length());
        
        // 更新性能统计
        UpdatePerformanceStats(duration, result.length());
        
    } catch (const std::exception& e) {
        LOGE("推理过程异常: %s", e.what());
        return "";
    }
    
    return result;
}

std::string InferenceEngine::StreamInference(const std::string& prompt, 
                                           int max_tokens,
                                           std::function<void(const std::string&)> token_callback) {
    if (!initialized_) {
        LOGE("推理引擎未初始化");
        return "";
    }
    
    std::string full_response;
    
    try {
        switch (backend_type_) {
            case BackendType::LLAMA_CPP:
                full_response = StreamInferenceWithLlamaCpp(prompt, max_tokens, token_callback);
                break;
            case BackendType::ONNX_RUNTIME:
                // ONNX Runtime 流式推理实现
                full_response = StreamInferenceWithOnnxRuntime(prompt, max_tokens, token_callback);
                break;
            default:
                LOGE("未知的推理后端类型");
                return "";
        }
    } catch (const std::exception& e) {
        LOGE("流式推理过程异常: %s", e.what());
        return "";
    }
    
    return full_response;
}

void InferenceEngine::Release() {
    if (!initialized_) {
        return;
    }
    
    LOGI("释放推理引擎资源");
    
    try {
        switch (backend_type_) {
            case BackendType::LLAMA_CPP:
                ReleaseLlamaCpp();
                break;
            case BackendType::ONNX_RUNTIME:
                ReleaseOnnxRuntime();
                break;
        }
        
        model_handle_ = nullptr;
        initialized_ = false;
        
        LOGI("推理引擎资源释放完成");
        
    } catch (const std::exception& e) {
        LOGE("释放推理引擎资源异常: %s", e.what());
    }
}

PerformanceStats InferenceEngine::GetPerformanceStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return performance_stats_;
}

bool InferenceEngine::InitializeLlamaCpp(const std::string& model_path) {
    LOGI("初始化 llama.cpp 后端");
    
#ifdef USE_LLAMA_CPP
    try {
        // 初始化 llama.cpp 模型参数
        llama_model_params model_params = llama_model_default_params();
        model_params.n_gpu_layers = 0; // CPU 推理
        model_params.use_mmap = true;
        model_params.use_mlock = false;
        
        // 加载模型
        llama_model* model = llama_load_model_from_file(model_path.c_str(), model_params);
        if (!model) {
            LOGE("加载 llama.cpp 模型失败: %s", model_path.c_str());
            return false;
        }
        
        // 创建上下文
        llama_context_params ctx_params = llama_context_default_params();
        ctx_params.seed = -1;
        ctx_params.n_ctx = context_length_;
        ctx_params.n_threads = threads_;
        ctx_params.n_threads_batch = threads_;
        
        llama_context* ctx = llama_new_context_with_model(model, ctx_params);
        if (!ctx) {
            LOGE("创建 llama.cpp 上下文失败");
            llama_free_model(model);
            return false;
        }
        
        // 保存句柄
        model_handle_ = ctx;
        llama_model_ = model;
        
        LOGI("llama.cpp 后端初始化成功");
        return true;
        
    } catch (const std::exception& e) {
        LOGE("初始化 llama.cpp 后端异常: %s", e.what());
        return false;
    }
#else
    LOGE("llama.cpp 后端未编译");
    return false;
#endif
}

bool InferenceEngine::InitializeOnnxRuntime(const std::string& model_path) {
    LOGI("初始化 ONNX Runtime 后端");
    
#ifdef USE_ONNX_RUNTIME
    try {
        // 创建 ONNX Runtime 环境
        onnx_env_ = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "DroidRunHP");
        
        // 创建会话选项
        Ort::SessionOptions session_options;
        session_options.SetIntraOpNumThreads(threads_);
        session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);
        
        // 如果支持GPU，启用GPU提供程序
        if (IsGPUAvailable()) {
            LOGI("启用GPU加速");
            // 添加GPU提供程序（如CUDA、OpenCL等）
        }
        
        // 创建推理会话
        onnx_session_ = std::make_unique<Ort::Session>(*onnx_env_, model_path.c_str(), session_options);
        
        // 获取输入输出信息
        GetModelInputOutputInfo();
        
        LOGI("ONNX Runtime 后端初始化成功");
        return true;
        
    } catch (const std::exception& e) {
        LOGE("初始化 ONNX Runtime 后端异常: %s", e.what());
        return false;
    }
#else
    LOGE("ONNX Runtime 后端未编译");
    return false;
#endif
}

std::string InferenceEngine::InferenceWithLlamaCpp(const std::string& prompt, int max_tokens) {
#ifdef USE_LLAMA_CPP
    if (!model_handle_ || !llama_model_) {
        LOGE("llama.cpp 模型未初始化");
        return "";
    }
    
    llama_context* ctx = static_cast<llama_context*>(model_handle_);
    
    // 对输入进行分词
    std::vector<llama_token> tokens = llama_tokenize(ctx, prompt, true);
    
    if (tokens.empty()) {
        LOGE("分词失败");
        return "";
    }
    
    // 评估输入tokens
    if (llama_decode(ctx, llama_batch_get_one(tokens.data(), tokens.size(), 0, 0))) {
        LOGE("评估输入tokens失败");
        return "";
    }
    
    std::string result;
    std::vector<llama_token> output_tokens;
    
    // 生成输出tokens
    for (int i = 0; i < max_tokens; ++i) {
        llama_token next_token = SampleNextToken(ctx);
        
        if (next_token == llama_token_eos(llama_get_model(ctx))) {
            break; // 遇到结束符
        }
        
        output_tokens.push_back(next_token);
        
        // 将token转换为文本
        char token_str[256];
        int token_len = llama_token_to_piece(llama_get_model(ctx), next_token, token_str, sizeof(token_str));
        if (token_len > 0) {
            result.append(token_str, token_len);
        }
        
        // 继续生成下一个token
        if (llama_decode(ctx, llama_batch_get_one(&next_token, 1, tokens.size() + i, 0))) {
            LOGE("生成下一个token失败");
            break;
        }
    }
    
    return result;
#else
    return "";
#endif
}

std::string InferenceEngine::InferenceWithOnnxRuntime(const std::string& prompt, int max_tokens) {
#ifdef USE_ONNX_RUNTIME
    if (!onnx_session_) {
        LOGE("ONNX Runtime 会话未初始化");
        return "";
    }
    
    try {
        // 预处理输入
        auto input_tensors = PreprocessInput(prompt);
        
        // 执行推理
        auto output_tensors = onnx_session_->Run(
            Ort::RunOptions{nullptr},
            input_names_.data(),
            input_tensors.data(),
            input_tensors.size(),
            output_names_.data(),
            output_names_.size()
        );
        
        // 后处理输出
        std::string result = PostprocessOutput(output_tensors, max_tokens);
        
        return result;
        
    } catch (const std::exception& e) {
        LOGE("ONNX Runtime 推理异常: %s", e.what());
        return "";
    }
#else
    return "";
#endif
}

void InferenceEngine::WarmupModel() {
    LOGI("开始预热模型");
    
    std::string warmup_prompt = "Hello";
    std::string result = Inference(warmup_prompt, 10);
    
    LOGI("模型预热完成，输出: %s", result.c_str());
}

void InferenceEngine::UpdatePerformanceStats(long duration_ms, size_t output_length) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    performance_stats_.total_inferences++;
    performance_stats_.total_time_ms += duration_ms;
    performance_stats_.total_tokens += output_length;
    
    if (duration_ms < performance_stats_.min_time_ms || performance_stats_.min_time_ms == 0) {
        performance_stats_.min_time_ms = duration_ms;
    }
    
    if (duration_ms > performance_stats_.max_time_ms) {
        performance_stats_.max_time_ms = duration_ms;
    }
    
    performance_stats_.avg_time_ms = performance_stats_.total_time_ms / performance_stats_.total_inferences;
    performance_stats_.tokens_per_second = (performance_stats_.total_tokens * 1000.0) / performance_stats_.total_time_ms;
}

#ifdef USE_LLAMA_CPP
llama_token InferenceEngine::SampleNextToken(llama_context* ctx) {
    auto* logits = llama_get_logits(ctx);
    auto n_vocab = llama_n_vocab(llama_get_model(ctx));
    
    // 简单的贪婪采样
    llama_token max_token = 0;
    float max_logit = logits[0];
    
    for (llama_token token = 1; token < n_vocab; ++token) {
        if (logits[token] > max_logit) {
            max_logit = logits[token];
            max_token = token;
        }
    }
    
    return max_token;
}
#endif

bool InferenceEngine::IsGPUAvailable() {
    // 检查GPU可用性的实现
    return false; // 简化实现，默认不使用GPU
}

} // namespace llm
} // namespace droidrun
