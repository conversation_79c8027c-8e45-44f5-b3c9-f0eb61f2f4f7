#include <jni.h>
#include <string>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <android/log.h>

#include "../llm/inference_engine.h"
#include "../vector/vector_database.h"
#include "../ui/ui_analyzer.h"

#define LOG_TAG "DroidRunHP_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

using namespace droidrun;

// 全局对象管理
static std::mutex g_engines_mutex;
static std::unordered_map<jlong, std::unique_ptr<llm::InferenceEngine>> g_llm_engines;
static std::unordered_map<jlong, std::unique_ptr<vector::VectorDatabase>> g_vector_dbs;
static std::unordered_map<jlong, std::unique_ptr<ui::UIAnalyzer>> g_ui_analyzers;

// 辅助函数：Java字符串转C++字符串
std::string jstring_to_string(JNIEnv* env, jstring jstr) {
    if (!jstr) return "";
    
    const char* chars = env->GetStringUTFChars(jstr, nullptr);
    std::string result(chars);
    env->ReleaseStringUTFChars(jstr, chars);
    return result;
}

// 辅助函数：C++字符串转Java字符串
jstring string_to_jstring(JNIEnv* env, const std::string& str) {
    return env->NewStringUTF(str.c_str());
}

// 辅助函数：生成唯一句柄
jlong generate_handle() {
    static std::atomic<jlong> counter{1};
    return counter.fetch_add(1);
}

extern "C" {

// ================================
// LLM 推理引擎 JNI 接口
// ================================

JNIEXPORT jlong JNICALL
Java_com_droidrun_hp_core_engine_LocalLLMEngine_initializeNativeEngine(
    JNIEnv* env, jobject /* this */, jstring model_path, jint context_length, jint threads) {
    
    try {
        std::string path = jstring_to_string(env, model_path);
        LOGI("初始化LLM引擎: %s", path.c_str());
        
        auto engine = std::make_unique<llm::InferenceEngine>();
        
        if (!engine->Initialize(path, context_length, threads)) {
            LOGE("LLM引擎初始化失败");
            return 0;
        }
        
        jlong handle = generate_handle();
        
        {
            std::lock_guard<std::mutex> lock(g_engines_mutex);
            g_llm_engines[handle] = std::move(engine);
        }
        
        LOGI("LLM引擎初始化成功，句柄: %ld", handle);
        return handle;
        
    } catch (const std::exception& e) {
        LOGE("初始化LLM引擎异常: %s", e.what());
        return 0;
    }
}

JNIEXPORT jstring JNICALL
Java_com_droidrun_hp_core_engine_LocalLLMEngine_performInference(
    JNIEnv* env, jobject /* this */, jlong handle, jstring prompt, jint max_tokens) {
    
    try {
        std::lock_guard<std::mutex> lock(g_engines_mutex);
        auto it = g_llm_engines.find(handle);
        if (it == g_llm_engines.end()) {
            LOGE("无效的LLM引擎句柄: %ld", handle);
            return string_to_jstring(env, "");
        }
        
        std::string input = jstring_to_string(env, prompt);
        std::string result = it->second->Inference(input, max_tokens);
        
        return string_to_jstring(env, result);
        
    } catch (const std::exception& e) {
        LOGE("LLM推理异常: %s", e.what());
        return string_to_jstring(env, "");
    }
}

JNIEXPORT jstring JNICALL
Java_com_droidrun_hp_core_engine_LocalLLMEngine_performStreamInference(
    JNIEnv* env, jobject thiz, jlong handle, jstring prompt, jint max_tokens, jobject callback) {
    
    try {
        std::lock_guard<std::mutex> lock(g_engines_mutex);
        auto it = g_llm_engines.find(handle);
        if (it == g_llm_engines.end()) {
            LOGE("无效的LLM引擎句柄: %ld", handle);
            return string_to_jstring(env, "");
        }
        
        std::string input = jstring_to_string(env, prompt);
        
        // 获取回调方法
        jclass callback_class = env->GetObjectClass(callback);
        jmethodID callback_method = env->GetMethodID(callback_class, "invoke", "(Ljava/lang/String;)V");
        
        if (!callback_method) {
            LOGE("找不到回调方法");
            return string_to_jstring(env, "");
        }
        
        // 创建全局引用，避免在回调中被GC
        jobject global_callback = env->NewGlobalRef(callback);
        JavaVM* jvm;
        env->GetJavaVM(&jvm);
        
        std::string result = it->second->StreamInference(input, max_tokens, 
            [jvm, global_callback, callback_method](const std::string& token) {
                JNIEnv* callback_env;
                if (jvm->AttachCurrentThread(&callback_env, nullptr) == JNI_OK) {
                    jstring jtoken = string_to_jstring(callback_env, token);
                    callback_env->CallVoidMethod(global_callback, callback_method, jtoken);
                    callback_env->DeleteLocalRef(jtoken);
                    jvm->DetachCurrentThread();
                }
            });
        
        // 清理全局引用
        env->DeleteGlobalRef(global_callback);
        
        return string_to_jstring(env, result);
        
    } catch (const std::exception& e) {
        LOGE("LLM流式推理异常: %s", e.what());
        return string_to_jstring(env, "");
    }
}

JNIEXPORT void JNICALL
Java_com_droidrun_hp_core_engine_LocalLLMEngine_releaseNativeEngine(
    JNIEnv* env, jobject /* this */, jlong handle) {
    
    try {
        std::lock_guard<std::mutex> lock(g_engines_mutex);
        auto it = g_llm_engines.find(handle);
        if (it != g_llm_engines.end()) {
            it->second->Release();
            g_llm_engines.erase(it);
            LOGI("LLM引擎已释放，句柄: %ld", handle);
        }
    } catch (const std::exception& e) {
        LOGE("释放LLM引擎异常: %s", e.what());
    }
}

// ================================
// 向量数据库 JNI 接口
// ================================

JNIEXPORT jlong JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_initializeVectorDB(
    JNIEnv* env, jobject /* this */, jint cache_size) {
    
    try {
        LOGI("初始化向量数据库，缓存大小: %d", cache_size);
        
        auto vector_db = std::make_unique<vector::VectorDatabase>();
        
        if (!vector_db->Initialize(cache_size)) {
            LOGE("向量数据库初始化失败");
            return 0;
        }
        
        jlong handle = generate_handle();
        
        {
            std::lock_guard<std::mutex> lock(g_engines_mutex);
            g_vector_dbs[handle] = std::move(vector_db);
        }
        
        LOGI("向量数据库初始化成功，句柄: %ld", handle);
        return handle;
        
    } catch (const std::exception& e) {
        LOGE("初始化向量数据库异常: %s", e.what());
        return 0;
    }
}

JNIEXPORT jlong JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_initializeEmbeddingModel(
    JNIEnv* env, jobject /* this */, jstring model_path) {
    
    try {
        std::string path = jstring_to_string(env, model_path);
        LOGI("初始化嵌入模型: %s", path.c_str());
        
        // 这里应该初始化嵌入模型，简化实现返回固定句柄
        jlong handle = generate_handle();
        
        LOGI("嵌入模型初始化成功，句柄: %ld", handle);
        return handle;
        
    } catch (const std::exception& e) {
        LOGE("初始化嵌入模型异常: %s", e.what());
        return 0;
    }
}

JNIEXPORT jfloatArray JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_computeEmbeddingNative(
    JNIEnv* env, jobject /* this */, jlong model_handle, jstring text) {
    
    try {
        std::string input = jstring_to_string(env, text);
        
        // 简化实现：生成固定维度的随机向量
        // 实际实现应该调用真正的嵌入模型
        const int embedding_dim = 384; // 常见的嵌入维度
        std::vector<float> embedding(embedding_dim);
        
        // 简单的哈希函数生成伪嵌入向量
        std::hash<std::string> hasher;
        size_t hash_value = hasher(input);
        
        for (int i = 0; i < embedding_dim; ++i) {
            embedding[i] = static_cast<float>((hash_value + i) % 1000) / 1000.0f - 0.5f;
        }
        
        // 转换为Java数组
        jfloatArray result = env->NewFloatArray(embedding_dim);
        env->SetFloatArrayRegion(result, 0, embedding_dim, embedding.data());
        
        return result;
        
    } catch (const std::exception& e) {
        LOGE("计算嵌入向量异常: %s", e.what());
        return nullptr;
    }
}

JNIEXPORT void JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_addVectorToDB(
    JNIEnv* env, jobject /* this */, jlong db_handle, jstring id, jfloatArray vector) {
    
    try {
        std::lock_guard<std::mutex> lock(g_engines_mutex);
        auto it = g_vector_dbs.find(db_handle);
        if (it == g_vector_dbs.end()) {
            LOGE("无效的向量数据库句柄: %ld", db_handle);
            return;
        }
        
        std::string vector_id = jstring_to_string(env, id);
        
        // 获取向量数据
        jsize vector_len = env->GetArrayLength(vector);
        jfloat* vector_data = env->GetFloatArrayElements(vector, nullptr);
        
        std::vector<float> embedding(vector_data, vector_data + vector_len);
        
        // 添加到数据库
        it->second->AddVector(vector_id, embedding);
        
        // 释放数组
        env->ReleaseFloatArrayElements(vector, vector_data, JNI_ABORT);
        
    } catch (const std::exception& e) {
        LOGE("添加向量到数据库异常: %s", e.what());
    }
}

JNIEXPORT jobject JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_searchSimilarVectors(
    JNIEnv* env, jobject /* this */, jlong db_handle, jfloatArray query_vector, 
    jint max_results, jfloat threshold) {
    
    try {
        std::lock_guard<std::mutex> lock(g_engines_mutex);
        auto it = g_vector_dbs.find(db_handle);
        if (it == g_vector_dbs.end()) {
            LOGE("无效的向量数据库句柄: %ld", db_handle);
            return nullptr;
        }
        
        // 获取查询向量
        jsize vector_len = env->GetArrayLength(query_vector);
        jfloat* vector_data = env->GetFloatArrayElements(query_vector, nullptr);
        
        std::vector<float> query(vector_data, vector_data + vector_len);
        
        // 执行搜索
        auto results = it->second->SearchSimilar(query, max_results, threshold);
        
        // 释放数组
        env->ReleaseFloatArrayElements(query_vector, vector_data, JNI_ABORT);
        
        // 创建Java List返回结果
        jclass list_class = env->FindClass("java/util/ArrayList");
        jmethodID list_constructor = env->GetMethodID(list_class, "<init>", "()V");
        jmethodID list_add = env->GetMethodID(list_class, "add", "(Ljava/lang/Object;)Z");
        
        jobject result_list = env->NewObject(list_class, list_constructor);
        
        // 创建Pair类
        jclass pair_class = env->FindClass("kotlin/Pair");
        jmethodID pair_constructor = env->GetMethodID(pair_class, "<init>", 
            "(Ljava/lang/Object;Ljava/lang/Object;)V");
        
        for (const auto& result : results) {
            jstring id_str = string_to_jstring(env, result.first);
            jobject similarity_obj = env->NewObject(
                env->FindClass("java/lang/Float"), 
                env->GetMethodID(env->FindClass("java/lang/Float"), "<init>", "(F)V"),
                result.second
            );
            
            jobject pair = env->NewObject(pair_class, pair_constructor, id_str, similarity_obj);
            env->CallBooleanMethod(result_list, list_add, pair);
            
            env->DeleteLocalRef(id_str);
            env->DeleteLocalRef(similarity_obj);
            env->DeleteLocalRef(pair);
        }
        
        return result_list;
        
    } catch (const std::exception& e) {
        LOGE("搜索相似向量异常: %s", e.what());
        return nullptr;
    }
}

JNIEXPORT void JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_releaseVectorDB(
    JNIEnv* env, jobject /* this */, jlong handle) {
    
    try {
        std::lock_guard<std::mutex> lock(g_engines_mutex);
        auto it = g_vector_dbs.find(handle);
        if (it != g_vector_dbs.end()) {
            it->second->Release();
            g_vector_dbs.erase(it);
            LOGI("向量数据库已释放，句柄: %ld", handle);
        }
    } catch (const std::exception& e) {
        LOGE("释放向量数据库异常: %s", e.what());
    }
}

JNIEXPORT void JNICALL
Java_com_droidrun_hp_core_engine_LocalRAGEngine_releaseEmbeddingModel(
    JNIEnv* env, jobject /* this */, jlong handle) {
    
    try {
        // 简化实现，实际应该释放嵌入模型资源
        LOGI("嵌入模型已释放，句柄: %ld", handle);
    } catch (const std::exception& e) {
        LOGE("释放嵌入模型异常: %s", e.what());
    }
}

} // extern "C"
