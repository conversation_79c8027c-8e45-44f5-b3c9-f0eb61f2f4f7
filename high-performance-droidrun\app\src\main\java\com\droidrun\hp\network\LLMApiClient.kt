package com.droidrun.hp.network

import com.droidrun.hp.data.model.*
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 轻量级LLM API客户端
 * 支持多种LLM提供商的API调用
 */
@Singleton
class LLMApiClient @Inject constructor() {
    
    companion object {
        private const val TIMEOUT_SECONDS = 30L
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 1000L
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .addInterceptor(LoggingInterceptor())
        .addInterceptor(RetryInterceptor())
        .build()
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    /**
     * 调用LLM API
     */
    suspend fun callLLM(request: LLMRequest): LLMResponse {
        return PerformanceProfiler.measureExecutionTimeAsync("LLMApiClient.callLLM") {
            when (request.provider) {
                LLMProvider.OPENAI -> callOpenAI(request)
                LLMProvider.ANTHROPIC -> callAnthropic(request)
                LLMProvider.GEMINI -> callGemini(request)
                LLMProvider.OLLAMA -> callOllama(request)
                LLMProvider.CUSTOM -> callCustom(request)
            }
        }
    }
    
    /**
     * 批量调用LLM API
     */
    suspend fun batchCallLLM(requests: List<LLMRequest>): List<LLMResponse> {
        return withContext(Dispatchers.IO) {
            requests.map { request ->
                async { callLLM(request) }
            }.awaitAll()
        }
    }
    
    /**
     * 调用OpenAI API
     */
    private suspend fun callOpenAI(request: LLMRequest): LLMResponse {
        val requestBody = buildOpenAIRequest(request)
        val httpRequest = Request.Builder()
            .url(request.baseUrl ?: "https://api.openai.com/v1/chat/completions")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("Authorization", "Bearer ${request.apiKey}")
            .addHeader("Content-Type", "application/json")
            .build()
        
        return executeRequest(httpRequest, request) { responseBody ->
            parseOpenAIResponse(responseBody, request)
        }
    }
    
    /**
     * 调用Anthropic API
     */
    private suspend fun callAnthropic(request: LLMRequest): LLMResponse {
        val requestBody = buildAnthropicRequest(request)
        val httpRequest = Request.Builder()
            .url(request.baseUrl ?: "https://api.anthropic.com/v1/messages")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("x-api-key", request.apiKey)
            .addHeader("Content-Type", "application/json")
            .addHeader("anthropic-version", "2023-06-01")
            .build()
        
        return executeRequest(httpRequest, request) { responseBody ->
            parseAnthropicResponse(responseBody, request)
        }
    }
    
    /**
     * 调用Gemini API
     */
    private suspend fun callGemini(request: LLMRequest): LLMResponse {
        val requestBody = buildGeminiRequest(request)
        val url = "${request.baseUrl ?: "https://generativelanguage.googleapis.com/v1/models"}/${request.model}:generateContent?key=${request.apiKey}"
        
        val httpRequest = Request.Builder()
            .url(url)
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("Content-Type", "application/json")
            .build()
        
        return executeRequest(httpRequest, request) { responseBody ->
            parseGeminiResponse(responseBody, request)
        }
    }
    
    /**
     * 调用Ollama API
     */
    private suspend fun callOllama(request: LLMRequest): LLMResponse {
        val requestBody = buildOllamaRequest(request)
        val httpRequest = Request.Builder()
            .url("${request.baseUrl ?: "http://localhost:11434"}/api/generate")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("Content-Type", "application/json")
            .build()
        
        return executeRequest(httpRequest, request) { responseBody ->
            parseOllamaResponse(responseBody, request)
        }
    }
    
    /**
     * 调用自定义API
     */
    private suspend fun callCustom(request: LLMRequest): LLMResponse {
        // 自定义API实现，可以根据需要扩展
        return LLMResponse(
            id = "custom_${System.currentTimeMillis()}",
            content = "自定义API响应",
            model = request.model,
            usage = LLMUsage(0, 0, 0),
            finishReason = "stop"
        )
    }
    
    /**
     * 执行HTTP请求
     */
    private suspend fun <T> executeRequest(
        request: Request,
        llmRequest: LLMRequest,
        parser: (String) -> T
    ): T {
        return withContext(Dispatchers.IO) {
            var lastException: Exception? = null
            
            repeat(MAX_RETRIES) { attempt ->
                try {
                    val response = httpClient.newCall(request).execute()
                    
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string() ?: ""
                        return@withContext parser(responseBody)
                    } else {
                        throw ApiException("HTTP ${response.code}: ${response.message}")
                    }
                    
                } catch (e: Exception) {
                    lastException = e
                    Timber.w("API调用失败 (尝试 ${attempt + 1}/$MAX_RETRIES): ${e.message}")
                    
                    if (attempt < MAX_RETRIES - 1) {
                        delay(RETRY_DELAY_MS * (attempt + 1))
                    }
                }
            }
            
            throw lastException ?: Exception("API调用失败")
        }
    }
    
    /**
     * 构建OpenAI请求体
     */
    private fun buildOpenAIRequest(request: LLMRequest): String {
        return json.encodeToString(
            OpenAIRequest.serializer(),
            OpenAIRequest(
                model = request.model,
                messages = listOf(
                    OpenAIMessage(role = "user", content = request.prompt)
                ),
                maxTokens = request.maxTokens,
                temperature = request.temperature,
                stream = false
            )
        )
    }
    
    /**
     * 构建Anthropic请求体
     */
    private fun buildAnthropicRequest(request: LLMRequest): String {
        return json.encodeToString(
            AnthropicRequest.serializer(),
            AnthropicRequest(
                model = request.model,
                maxTokens = request.maxTokens,
                messages = listOf(
                    AnthropicMessage(role = "user", content = request.prompt)
                ),
                temperature = request.temperature
            )
        )
    }
    
    /**
     * 构建Gemini请求体
     */
    private fun buildGeminiRequest(request: LLMRequest): String {
        return json.encodeToString(
            GeminiRequest.serializer(),
            GeminiRequest(
                contents = listOf(
                    GeminiContent(
                        parts = listOf(GeminiPart(text = request.prompt))
                    )
                ),
                generationConfig = GeminiGenerationConfig(
                    maxOutputTokens = request.maxTokens,
                    temperature = request.temperature
                )
            )
        )
    }
    
    /**
     * 构建Ollama请求体
     */
    private fun buildOllamaRequest(request: LLMRequest): String {
        return json.encodeToString(
            OllamaRequest.serializer(),
            OllamaRequest(
                model = request.model,
                prompt = request.prompt,
                stream = false,
                options = OllamaOptions(
                    temperature = request.temperature,
                    numPredict = request.maxTokens
                )
            )
        )
    }
    
    /**
     * 解析OpenAI响应
     */
    private fun parseOpenAIResponse(responseBody: String, request: LLMRequest): LLMResponse {
        val response = json.decodeFromString<OpenAIResponse>(responseBody)
        return LLMResponse(
            id = response.id,
            content = response.choices.firstOrNull()?.message?.content ?: "",
            model = response.model,
            usage = LLMUsage(
                promptTokens = response.usage?.promptTokens ?: 0,
                completionTokens = response.usage?.completionTokens ?: 0,
                totalTokens = response.usage?.totalTokens ?: 0
            ),
            finishReason = response.choices.firstOrNull()?.finishReason ?: "unknown"
        )
    }
    
    /**
     * 解析Anthropic响应
     */
    private fun parseAnthropicResponse(responseBody: String, request: LLMRequest): LLMResponse {
        val response = json.decodeFromString<AnthropicResponse>(responseBody)
        return LLMResponse(
            id = response.id,
            content = response.content.firstOrNull()?.text ?: "",
            model = response.model,
            usage = LLMUsage(
                promptTokens = response.usage.inputTokens,
                completionTokens = response.usage.outputTokens,
                totalTokens = response.usage.inputTokens + response.usage.outputTokens
            ),
            finishReason = response.stopReason ?: "unknown"
        )
    }
    
    /**
     * 解析Gemini响应
     */
    private fun parseGeminiResponse(responseBody: String, request: LLMRequest): LLMResponse {
        val response = json.decodeFromString<GeminiResponse>(responseBody)
        return LLMResponse(
            id = "gemini_${System.currentTimeMillis()}",
            content = response.candidates.firstOrNull()?.content?.parts?.firstOrNull()?.text ?: "",
            model = request.model,
            usage = LLMUsage(0, 0, 0), // Gemini不返回token使用信息
            finishReason = response.candidates.firstOrNull()?.finishReason ?: "unknown"
        )
    }
    
    /**
     * 解析Ollama响应
     */
    private fun parseOllamaResponse(responseBody: String, request: LLMRequest): LLMResponse {
        val response = json.decodeFromString<OllamaResponse>(responseBody)
        return LLMResponse(
            id = "ollama_${System.currentTimeMillis()}",
            content = response.response,
            model = response.model,
            usage = LLMUsage(0, 0, 0), // Ollama不返回详细token信息
            finishReason = if (response.done) "stop" else "unknown"
        )
    }
    
    /**
     * 日志拦截器
     */
    private class LoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val startTime = System.currentTimeMillis()
            
            val response = chain.proceed(request)
            
            val duration = System.currentTimeMillis() - startTime
            Timber.d("API请求: ${request.url} - ${response.code} (${duration}ms)")
            
            return response
        }
    }
    
    /**
     * 重试拦截器
     */
    private class RetryInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            var response = chain.proceed(request)
            
            // 对于5xx错误进行重试
            if (response.code in 500..599) {
                response.close()
                response = chain.proceed(request)
            }
            
            return response
        }
    }
    
    /**
     * API异常
     */
    class ApiException(message: String) : Exception(message)
}
