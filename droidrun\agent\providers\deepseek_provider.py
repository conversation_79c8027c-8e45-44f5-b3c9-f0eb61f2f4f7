"""
DeepSeek provider implementation.
"""

import os
import asyncio
import base64
import logging
from typing import Optional

from openai import OpenAI
from ..llm_provider import LLMProvider

logger = logging.getLogger("droidrun")

class DeepSeekProvider(LLMProvider):
    """DeepSeek provider implementation."""
    def _initialize_client(self) -> None:
        self.api_key = self.api_key or os.environ.get("DEEPSEEK_API_KEY")
        self.base_url = self.base_url or os.environ.get("DEEPSEEK_BASE_URL")
        self.model_name = self.model_name or os.environ.get("DEEPSEEK_MODEL")
        if not self.api_key:
            raise ValueError("DeepSeek API key 未提供，也未在环境变量中找到")
        if not self.base_url:
            self.base_url = "https://api.deepseek.com/v1"  # 默认值，可根据实际情况修改
        if not self.model_name:
            self.model_name = "deepseek-chat"
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
        logger.info(f"Initialized DeepSeek client with model {self.model_name} at {self.base_url}")

    async def generate_response(
        self,
        system_prompt: str,
        user_prompt: str,
        screenshot_data: Optional[bytes] = None
    ) -> str:
        try:
            messages = [
                {"role": "system", "content": system_prompt},
            ]
            if screenshot_data:
                base64_image = base64.b64encode(screenshot_data).decode('utf-8')
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Here's the current screenshot of the device. Please analyze it to help with the next action."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                })
            messages.append({"role": "user", "content": user_prompt})
            
            # 打印请求参数
            logger.info("===== DeepSeek API Request Parameters =====")
            logger.info(f"API URL: {self.base_url}")
            logger.info(f"Model: {self.model_name}")
            logger.info(f"Temperature: {self.temperature}")
            logger.info(f"Max tokens: {self.max_tokens}")
            logger.info(f"System prompt length: {len(system_prompt)} chars")
            logger.info(f"User prompt: {user_prompt}")
            logger.info(f"Has screenshot: {screenshot_data is not None}")
            logger.info("=========================================")
            
            response = await asyncio.to_thread(
                self.client.chat.completions.create,
                model=self.model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                response_format={"type": "json_object"}
            )
            usage = response.usage
            self.update_token_usage(usage.prompt_tokens, usage.completion_tokens)
            logger.info("===== Token Usage Statistics =====")
            logger.info(f"API Call #{self.api_calls}")
            logger.info(f"This call: {usage.prompt_tokens} prompt + {usage.completion_tokens} completion = {usage.total_tokens} tokens")
            logger.info(f"Cumulative: {self.get_token_usage_stats()}")
            logger.info("=================================")
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error calling DeepSeek API: {e}")
            raise 