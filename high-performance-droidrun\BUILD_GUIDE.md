# 🔨 DroidRun高性能版构建指南

解决Windows环境下的构建问题，快速完成项目构建。

## 🚨 **问题诊断**

您遇到的错误：
```
./gradlew : 无法将"./gradlew"项识别为 cmdlet、函数、脚本文件或可运行程序的名称
```

**原因**: Windows PowerShell无法识别Unix风格的执行命令。

## ✅ **解决方案**

### **方案1: 使用Windows批处理文件**
```powershell
# 在PowerShell中执行
.\gradlew.bat assembleRelease
```

### **方案2: 使用PowerShell构建脚本**
```powershell
# 使用我们提供的构建脚本
.\build.ps1 -Task assembleRelease
```

### **方案3: 使用系统Gradle**
```powershell
# 如果已安装Gradle
gradle assembleRelease
```

## 🛠️ **完整构建步骤**

### **1. 环境检查**

#### **检查Java版本**
```powershell
java -version
```
**要求**: JDK 11或更高版本

#### **检查Android SDK**
```powershell
echo $env:ANDROID_HOME
```
**要求**: 设置ANDROID_HOME环境变量

#### **检查Gradle**
```powershell
gradle --version
```

### **2. 项目准备**

#### **下载依赖**
```powershell
.\gradlew.bat --refresh-dependencies
```

#### **清理项目**
```powershell
.\gradlew.bat clean
```

### **3. 构建项目**

#### **构建调试版本**
```powershell
.\gradlew.bat assembleDebug
```

#### **构建发布版本**
```powershell
.\gradlew.bat assembleRelease
```

#### **使用PowerShell脚本**
```powershell
# 构建调试版本
.\build.ps1

# 构建发布版本
.\build.ps1 -Task assembleRelease

# 清理并构建
.\build.ps1 -Clean -Task assembleRelease
```

## 🔧 **常见问题解决**

### **问题1: 找不到gradlew.bat**
```powershell
# 生成Gradle Wrapper
gradle wrapper --gradle-version 8.4
```

### **问题2: Java版本不兼容**
```powershell
# 下载并安装JDK 11+
# https://adoptium.net/
```

### **问题3: Android SDK未配置**
```powershell
# 设置环境变量
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
```

### **问题4: 网络连接问题**
```powershell
# 使用国内镜像
.\gradlew.bat assembleRelease -Dorg.gradle.jvmargs="-Dfile.encoding=UTF-8" --no-daemon
```

### **问题5: 内存不足**
```powershell
# 增加Gradle内存
$env:GRADLE_OPTS = "-Xmx4g -XX:MaxMetaspaceSize=512m"
```

## 📱 **构建输出**

成功构建后，APK文件位置：
```
app/build/outputs/apk/
├── debug/
│   └── app-debug.apk
└── release/
    └── app-release.apk
```

## 🚀 **快速开始脚本**

创建 `quick-build.bat` 文件：
```batch
@echo off
echo 🚀 DroidRun高性能版快速构建
echo ================================

echo 📋 检查环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或配置错误
    pause
    exit /b 1
)

echo 🧹 清理项目...
call gradlew.bat clean

echo 🔨 构建项目...
call gradlew.bat assembleRelease

if %errorlevel% equ 0 (
    echo ✅ 构建成功！
    echo 📦 APK位置: app\build\outputs\apk\release\app-release.apk
) else (
    echo ❌ 构建失败
)

pause
```

然后双击运行 `quick-build.bat`。

## 🔍 **调试构建问题**

### **详细日志**
```powershell
.\gradlew.bat assembleRelease --info --stacktrace
```

### **离线模式**
```powershell
.\gradlew.bat assembleRelease --offline
```

### **强制刷新**
```powershell
.\gradlew.bat assembleRelease --refresh-dependencies
```

### **清理Gradle缓存**
```powershell
.\gradlew.bat clean cleanBuildCache
```

## 📋 **构建检查清单**

- [ ] ✅ Java 11+ 已安装
- [ ] ✅ Android SDK 已配置
- [ ] ✅ ANDROID_HOME 环境变量已设置
- [ ] ✅ 网络连接正常
- [ ] ✅ gradlew.bat 文件存在
- [ ] ✅ 项目依赖已下载
- [ ] ✅ 构建缓存已清理

## 🆘 **紧急解决方案**

如果所有方法都失败，可以：

### **1. 使用Android Studio**
1. 打开Android Studio
2. 导入项目
3. 点击 Build → Build Bundle(s) / APK(s) → Build APK(s)

### **2. 手动构建**
1. 下载预编译的Gradle
2. 手动配置构建环境
3. 使用IDE构建

### **3. 容器化构建**
```dockerfile
# 使用Docker构建
docker run --rm -v ${PWD}:/workspace -w /workspace gradle:8.4-jdk11 gradle assembleRelease
```

## 📞 **获取帮助**

如果仍有问题，请提供：
1. 完整错误日志
2. Java版本信息
3. Android SDK路径
4. 操作系统版本
5. 网络环境信息

---

**构建成功后，请参考 `DOUBAO_SETUP_GUIDE.md` 进行豆包服务配置！** 🎉
