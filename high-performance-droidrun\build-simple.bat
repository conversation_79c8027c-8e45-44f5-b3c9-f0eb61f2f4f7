@echo off
echo DroidRun Simple Build
echo ====================

echo Setting environment...
set GRADLE_HOME=C:\gradle\gradle-8.4
set PATH=%GRADLE_HOME%\bin;%PATH%

echo Checking Gradle...
if not exist "%GRADLE_HOME%\bin\gradle.bat" (
    echo ERROR: Gradle not found
    echo Please run: install-gradle.bat
    pause
    exit /b 1
)

echo OK: Gradle found

echo Setting Java...
if exist "C:\java\jdk-17.0.9+9" (
    set JAVA_HOME=C:\java\jdk-17.0.9+9
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo Using Java 17
) else (
    echo Using system Java
)

echo Setting Android SDK...
if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
    set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
    echo Android SDK found
) else (
    echo WARNING: Android SDK not found
)

echo.
echo Starting build...
echo =================

echo Creating gradle.properties...
echo org.gradle.jvmargs=-Xmx4g > gradle.properties
echo org.gradle.daemon=true >> gradle.properties
echo android.useAndroidX=true >> gradle.properties
if defined JAVA_HOME echo org.gradle.java.home=%JAVA_HOME% >> gradle.properties

echo Cleaning...
"%GRADLE_HOME%\bin\gradle.bat" clean --no-daemon

echo Building...
"%GRADLE_HOME%\bin\gradle.bat" assembleRelease --no-daemon

if %errorlevel% equ 0 (
    echo.
    echo BUILD SUCCESS!
    echo =============
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK: app\build\outputs\apk\release\app-release.apk
        echo Install: adb install app\build\outputs\apk\release\app-release.apk
    ) else (
        echo APK not found, searching...
        dir /s *.apk
    )
) else (
    echo.
    echo BUILD FAILED!
    echo =============
    echo Check errors above
)

pause
