{"app_info": {"name": "DroidRun高性能版", "version": "1.0.0", "description": "基于豆包生态的Android自动化应用"}, "llm_config": {"provider": "deepseek", "api_key": "***********************************", "api_url": "https://api.deepseek.com/v1", "model": "deepseek-chat", "temperature": 0.2, "max_tokens": 512, "timeout_seconds": 30}, "embedding_config": {"provider": "do<PERSON>o", "api_key": "f891395f-e519-4807-8101-6be9c0b55d4d", "api_url": "https://ark.cn-beijing.volces.com/api/v3/embeddings", "model": "doubao-embedding-text-240715", "use_fallback": true}, "vector_db_config": {"provider": "viking_db", "collection": "droidRunRag", "host": "api-vikingdb.volces.com", "region": "cn-beijing", "access_key": "AKLTYTRmYzFjNTNlMjk1NGFmZGI2ODFkYmQyNjU3Mzk4NDg", "secret_key": "WVRjM1l6QmtNV0ZtTUdRNE5HVmhObUU1TkRsa05UZzJOemhpT0RNd01EYw==", "top_k": 5, "similarity_threshold": 0.7}, "cloud_kb_config": {"enabled": true, "collection_name": "droidRunRag", "project_name": "default", "domain": "api-knowledgebase.mlp.cn-beijing.volces.com", "top_k": 5, "score_threshold": 0.7}, "performance_config": {"local_decision_threshold": 0.8, "cache_config": {"memory_cache_size": 100, "disk_cache_size_mb": 50, "cleanup_interval_minutes": 5, "max_age_hours": 24}, "execution_config": {"max_concurrent_tasks": 3, "task_timeout_seconds": 300, "ui_operation_timeout_ms": 5000, "batch_size": 10}, "monitoring_config": {"enable_performance_monitoring": true, "enable_memory_monitoring": true, "enable_network_monitoring": true, "metrics_retention_days": 7}}, "ui_config": {"accessibility_service": {"event_types": ["typeAllMask"], "feedback_type": "feedbackGeneric", "flags": ["<PERSON><PERSON><PERSON><PERSON>", "flagRetrieveInteractiveWindows", "flagReportViewIds", "flagRequestTouchExplorationMode"], "notification_timeout": 0, "can_retrieve_window_content": true, "can_request_touch_exploration": true, "can_perform_gestures": true}, "operation_delays": {"click_delay_ms": 100, "input_delay_ms": 200, "scroll_delay_ms": 300, "swipe_delay_ms": 500}}, "security_config": {"encrypt_api_keys": true, "auto_clear_sensitive_data": true, "enable_analytics": false, "data_retention_days": 30, "log_sensitive_data": false}, "network_config": {"strategy": "wifi_preferred", "timeout_seconds": 30, "max_retries": 3, "retry_delay_ms": 1000, "enable_compression": true, "user_agent": "DroidRun-HP/1.0.0"}, "logging_config": {"level": "info", "enable_file_logging": true, "max_log_files": 5, "max_log_size_mb": 10, "log_retention_days": 7, "enable_crash_reporting": true}, "feature_flags": {"enable_experimental_features": false, "enable_voice_control": false, "enable_screenshot_analysis": true, "enable_gesture_recording": true, "enable_auto_update": true, "enable_fallback_embedding": true}, "knowledge_base": {"categories": ["android_ui_patterns", "common_operations", "app_specific_guides", "troubleshooting", "performance_tips"], "auto_update_enabled": true, "update_interval_hours": 24, "local_cache_enabled": true, "max_cache_entries": 1000}, "api_server": {"enabled": true, "port": 8080, "host": "0.0.0.0", "enable_cors": true, "enable_auth": false, "rate_limit": {"requests_per_minute": 60, "burst_size": 10}}, "model_configs": {"deepseek": {"models": [{"id": "deepseek-chat", "name": "DeepSeek Chat", "max_tokens": 4096, "cost_per_1k_tokens": 0.0014}, {"id": "deepseek-coder", "name": "DeepSeek Coder", "max_tokens": 4096, "cost_per_1k_tokens": 0.0014}]}, "doubao_embedding": {"models": [{"id": "doubao-embedding-text-240715", "name": "豆包文本嵌入", "dimension": 1536, "cost_per_1k_tokens": 0.0002}]}}, "fallback_config": {"enable_local_fallback": true, "local_models": {"embedding": "sentence-transformers/all-MiniLM-L6-v2", "llm": "microsoft/DialoGPT-medium"}, "fallback_threshold_ms": 10000}}