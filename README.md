# DroidRun - Android Automation with LLM Agents

DroidRun is a framework for controlling Android devices through LLM agents. It uses a ReAct (Reasoning + Acting) approach to automate tasks on Android devices.

## Features

- Control Android devices through natural language commands
- Use large language models (LLMs) to reason about device state and take actions
- Support for multiple LLM providers:
  - OpenAI (GPT-4, GPT-3.5)
  - Anthropic (Claude)
  - Google (Gemini)
  - Ollama (local models)
  - Doubao
  - Yuanbao
  - DeepSeek
- Local RAG knowledge base for offline operation
- Vision capabilities for screen understanding
- Memory for persistent information across steps

## Installation

```bash
pip install droidrun
```

## Quick Start

1. Connect your Android device via USB or ADB over TCP/IP
2. Set up your API keys in environment variables
3. Run a command:

```bash
droidrun "Open the settings app and enable dark mode"
```

## Using Local RAG Knowledge Base

You can use the local RAG (Retrieval-Augmented Generation) knowledge base instead of calling external LLM APIs:

```bash
droidrun rag "Open the settings app and enable dark mode"
```

This uses a local knowledge base running on http://127.0.0.1:8002/ask to generate responses.

## Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key
- `ANTHROPIC_API_KEY`: Your Anthropic API key
- `GEMINI_API_KEY`: Your Google Gemini API key
- `DOUBAO_API_KEY`: Your Doubao API key
- `YUANBAO_API_KEY`: Your Yuanbao API key
- `DEEPSEEK_API_KEY`: Your DeepSeek API key
- `DROIDRUN_DEVICE_SERIAL`: (Optional) Device serial to use

## Commands

- `droidrun run "command"`: Run a command using an LLM provider
- `droidrun rag "command"`: Run a command using the local RAG knowledge base
- `droidrun devices`: List connected devices
- `droidrun connect <ip>`: Connect to a device over TCP/IP
- `droidrun disconnect <serial>`: Disconnect from a device
- `droidrun setup --path <apk_path>`: Install and set up the DroidRun Portal app

## Identity Handling

When using the RAG agent, if you ask questions about the model's identity (like "who are you?" or "what model are you?"), it will respond with:

"我是由claude-4-sonnet-thinking模型支持的智能助手，专为Cursor IDE设计，可以帮您解决各类编程难题，请告诉我你需要什么帮助？"

## License

MIT 