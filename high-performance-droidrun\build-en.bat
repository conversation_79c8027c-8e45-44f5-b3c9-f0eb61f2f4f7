@echo off
title DroidRun Build Tool

echo.
echo DroidRun High-Performance Build Tool
echo ====================================
echo.

REM Check parameters
if "%1"=="help" goto :help
if "%1"=="--help" goto :help
if "%1"=="-h" goto :help

REM Set default task
set TASK=assembleDebug
if not "%1"=="" set TASK=%1

echo Checking build environment...
echo.

REM Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found
    echo Please install JDK 11 or higher
    echo Download: https://adoptium.net/
    echo.
    pause
    exit /b 1
) else (
    echo OK: Java environment ready
)

REM Check Android SDK
if defined ANDROID_HOME (
    echo OK: Android SDK found at %ANDROID_HOME%
) else (
    echo WARNING: ANDROID_HOME not set
    echo Please install Android Studio and set SDK path
)

REM Check Gradle Wrapper
if exist "gradlew.bat" (
    echo OK: Gradle Wrapper found
) else (
    echo ERROR: gradlew.bat not found
    echo Trying to generate...
    gradle wrapper --gradle-version 8.4 >nul 2>&1
    if %errorlevel% equ 0 (
        echo OK: Gradle Wrapper generated successfully
    ) else (
        echo ERROR: Cannot generate Gradle Wrapper
        echo Please install Gradle manually or use Android Studio
        pause
        exit /b 1
    )
)

echo.
echo Starting build task: %TASK%
echo ====================================
echo.

REM Execute build
call gradlew.bat %TASK% --stacktrace

if %errorlevel% equ 0 (
    echo.
    echo ====================================
    echo BUILD SUCCESSFUL!
    echo.
    
    REM Show APK location
    if "%TASK%"=="assembleDebug" (
        echo Debug APK location:
        if exist "app\build\outputs\apk\debug\app-debug.apk" (
            echo    app\build\outputs\apk\debug\app-debug.apk
        )
    )
    
    if "%TASK%"=="assembleRelease" (
        echo Release APK location:
        if exist "app\build\outputs\apk\release\app-release.apk" (
            echo    app\build\outputs\apk\release\app-release.apk
        )
    )
    
    echo.
    echo Next steps:
    echo    1. Install APK: adb install [APK_PATH]
    echo    2. Launch app and configure Doubao services
    echo    3. See DOUBAO_SETUP_GUIDE.md for configuration
    echo.
    
) else (
    echo.
    echo ====================================
    echo BUILD FAILED
    echo.
    echo Common solutions:
    echo    1. Check network connection
    echo    2. Clean project: build-en.bat clean
    echo    3. Check Android SDK configuration
    echo    4. Update Android Studio
    echo.
)

echo Press any key to exit...
pause >nul
exit /b %errorlevel%

:help
echo.
echo DroidRun High-Performance Build Tool
echo.
echo Usage:
echo   build-en.bat [task]
echo.
echo Common tasks:
echo   assembleDebug     Build debug version (default)
echo   assembleRelease   Build release version
echo   clean             Clean build files
echo   test              Run unit tests
echo.
echo Examples:
echo   build-en.bat                    Build debug version
echo   build-en.bat assembleRelease    Build release version
echo   build-en.bat clean              Clean project
echo.
echo Other options:
echo   build-en.bat help               Show this help
echo.
pause
exit /b 0
