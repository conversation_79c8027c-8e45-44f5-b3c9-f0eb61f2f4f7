# 🔧 Gradle Wrapper 修复指南

## 🚨 **问题诊断**

错误信息：
```
错误: 找不到或无法加载主类 org.gradle.wrapper.GradleWrapperMain
```

**原因**: `gradle-wrapper.jar` 文件缺失或损坏。

## ✅ **解决方案 (按优先级排序)**

### **方案1: 自动修复 Gradle Wrapper (推荐)**
```cmd
.\fix-gradle.bat
```
这个脚本会自动下载并修复 Gradle Wrapper。

### **方案2: 使用系统 Gradle**
```cmd
.\build-without-wrapper.bat
```
如果您已安装 Gradle，这个脚本会使用系统 Gradle 进行构建。

### **方案3: 使用 Android Studio (最可靠)**
```cmd
.\android-studio-guide.bat
```
查看详细的 Android Studio 构建指南。

### **方案4: 手动修复**

#### 4.1 下载 Gradle
```cmd
# 访问 https://gradle.org/releases/
# 下载 Gradle 8.4
# 解压到 C:\gradle
# 添加到 PATH: C:\gradle\bin
```

#### 4.2 生成 Wrapper
```cmd
gradle wrapper --gradle-version 8.4
```

#### 4.3 验证修复
```cmd
.\gradlew.bat --version
```

## 🎯 **推荐操作步骤**

### **步骤1: 尝试自动修复**
```cmd
.\fix-gradle.bat
```

### **步骤2: 如果修复成功，重新构建**
```cmd
.\gradlew.bat assembleRelease
```

### **步骤3: 如果仍然失败，使用系统 Gradle**
```cmd
.\build-without-wrapper.bat
```

### **步骤4: 最后选择 - Android Studio**
1. 下载安装 Android Studio
2. 打开项目文件夹
3. 等待同步完成
4. 使用 Build 菜单构建 APK

## 🔍 **环境检查**

### **检查 Java**
```cmd
java -version
```
需要 JDK 11 或更高版本。

### **检查 Android SDK**
```cmd
echo %ANDROID_HOME%
```
如果为空，需要安装 Android Studio。

### **检查 Gradle**
```cmd
gradle --version
```
如果可用，可以直接使用系统 Gradle。

## 📁 **文件结构检查**

确保以下文件存在：
```
high-performance-droidrun/
├── gradlew.bat ✅
├── gradlew ✅
├── gradle/
│   └── wrapper/
│       ├── gradle-wrapper.jar ❌ (这个文件缺失)
│       └── gradle-wrapper.properties ✅
└── build.gradle ✅
```

## 🆘 **如果所有方法都失败**

### **最简单的解决方案**
1. **下载并安装 Android Studio**
   - https://developer.android.com/studio
   - 包含完整的 Android 开发环境

2. **使用 Android Studio 打开项目**
   - File → Open → 选择项目文件夹
   - 等待同步完成

3. **在 Android Studio 中构建**
   - Build → Build Bundle(s) / APK(s) → Build APK(s)

### **或者使用在线构建服务**
- GitHub Actions
- GitLab CI
- 其他 CI/CD 服务

## 📞 **需要帮助？**

如果问题仍然存在，请提供：
1. Java 版本信息
2. 是否安装了 Android Studio
3. 是否安装了 Gradle
4. 完整的错误日志

---

**现在请尝试: `.\fix-gradle.bat`** 🚀
