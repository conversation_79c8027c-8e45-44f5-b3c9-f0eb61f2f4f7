@echo off
echo Fixing Gradle Wrapper...
echo ========================

echo Step 1: Creating gradle directory structure...
if not exist "gradle" mkdir gradle
if not exist "gradle\wrapper" mkdir gradle\wrapper

echo Step 2: Downloading Gradle Wrapper JAR...
echo This may take a moment...

REM Try to download gradle-wrapper.jar using PowerShell
powershell -Command "& {try { Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar' -UseBasicParsing; Write-Host 'Downloaded gradle-wrapper.jar successfully' } catch { Write-Host 'Download failed, trying alternative method...' }}"

REM Check if download was successful
if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo OK: gradle-wrapper.jar downloaded successfully
) else (
    echo WARNING: Could not download gradle-wrapper.jar
    echo Trying to generate using system Gradle...
    
    REM Try using system gradle if available
    gradle wrapper --gradle-version 8.4 >nul 2>&1
    if %errorlevel% equ 0 (
        echo OK: Gradle wrapper generated using system Gradle
    ) else (
        echo ERROR: Cannot fix Gradle wrapper automatically
        echo.
        echo Manual solutions:
        echo 1. Install Android Studio (includes Gradle)
        echo 2. Download Gradle manually from https://gradle.org/releases/
        echo 3. Use Android Studio to open and build the project
        echo.
        pause
        exit /b 1
    )
)

echo Step 3: Verifying Gradle wrapper...
if exist "gradle\wrapper\gradle-wrapper.jar" (
    if exist "gradle\wrapper\gradle-wrapper.properties" (
        echo OK: Gradle wrapper is ready
        echo.
        echo Now you can try building:
        echo   .\gradlew.bat assembleRelease
        echo.
    ) else (
        echo WARNING: gradle-wrapper.properties missing
    )
) else (
    echo ERROR: gradle-wrapper.jar still missing
)

pause
