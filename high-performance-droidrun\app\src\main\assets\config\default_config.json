{
  "app_info": {
    "name": "DroidRun高性能版",
    "version": "1.0.0",
    "build": 1,
    "description": "轻量级Android自动化应用"
  },
  
  "llm_config": {
    "providers": {
      "doubao": {
        "name": "豆包",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        "models": [
          {
            "id": "doubao-pro-4k",
            "name": "豆包-pro-4k",
            "max_tokens": 4096,
            "cost_per_1k_tokens": 0.0008
          },
          {
            "id": "doubao-pro-32k",
            "name": "豆包-pro-32k",
            "max_tokens": 32768,
            "cost_per_1k_tokens": 0.008
          },
          {
            "id": "doubao-lite-4k",
            "name": "豆包-lite-4k",
            "max_tokens": 4096,
            "cost_per_1k_tokens": 0.0003
          },
          {
            "id": "doubao-lite-32k",
            "name": "豆包-lite-32k",
            "max_tokens": 32768,
            "cost_per_1k_tokens": 0.003
          }
        ],
        "default_model": "doubao-pro-4k",
        "default_temperature": 0.2,
        "default_max_tokens": 512
      },

      "openai": {
        "name": "OpenAI",
        "base_url": "https://api.openai.com/v1",
        "models": [
          {
            "id": "gpt-4o",
            "name": "GPT-4o",
            "max_tokens": 4096,
            "cost_per_1k_tokens": 0.005
          },
          {
            "id": "gpt-4o-mini",
            "name": "GPT-4o-mini",
            "max_tokens": 16384,
            "cost_per_1k_tokens": 0.00015
          },
          {
            "id": "gpt-4-turbo",
            "name": "GPT-4-turbo",
            "max_tokens": 4096,
            "cost_per_1k_tokens": 0.01
          },
          {
            "id": "gpt-3.5-turbo",
            "name": "GPT-3.5-turbo",
            "max_tokens": 4096,
            "cost_per_1k_tokens": 0.0015
          }
        ],
        "default_model": "gpt-4o-mini",
        "default_temperature": 0.2,
        "default_max_tokens": 512

      
      "ollama": {
        "name": "Ollama",
        "base_url": "http://localhost:11434",
        "models": [
          {
            "id": "llama3.1:8b",
            "name": "Llama3.1:8b",
            "max_tokens": 2048,
            "cost_per_1k_tokens": 0
          },
          {
            "id": "llama3.1:70b",
            "name": "Llama3.1:70b",
            "max_tokens": 2048,
            "cost_per_1k_tokens": 0
          },
          {
            "id": "qwen2:7b",
            "name": "Qwen2:7b",
            "max_tokens": 2048,
            "cost_per_1k_tokens": 0
          },
          {
            "id": "phi3:mini",
            "name": "Phi3:mini",
            "max_tokens": 2048,
            "cost_per_1k_tokens": 0
          }
        ],
        "default_model": "llama3.1:8b",
        "default_temperature": 0.2,
        "default_max_tokens": 512
      }
    },
    
    "default_provider": "doubao",
    "timeout_seconds": 30,
    "max_retries": 3,
    "retry_delay_ms": 1000
  },
  
  "rag_config": {
    "providers": {
      "doubao_vector": {
        "name": "豆包向量库",
        "base_url": "https://ark.cn-beijing.volces.com",
        "default_collection": "droidrun_knowledge",
        "default_namespace": "android_automation",
        "vector_dimension": 1536,
        "metric": "cosine"
      },

      "pinecone": {
        "name": "Pinecone",
        "base_url": "https://api.pinecone.io",
        "default_index": "droidrun-knowledge",
        "default_namespace": "android-automation",
        "vector_dimension": 1536,
        "metric": "cosine"

    },
    
    "default_provider": "doubao_vector",
    "default_top_k": 5,
    "default_similarity_threshold": 0.7,
    "timeout_seconds": 15,
    "max_retries": 2,
    "enable_caching": true,
    "cache_ttl_minutes": 30
  },
  
  "performance_config": {
    "local_decision_threshold": 0.8,
    "cache_config": {
      "memory_cache_size": 100,
      "disk_cache_size_mb": 50,
      "cleanup_interval_minutes": 5,
      "max_age_hours": 24
    },
    "execution_config": {
      "max_concurrent_tasks": 3,
      "task_timeout_seconds": 300,
      "ui_operation_timeout_ms": 5000,
      "batch_size": 10
    },
    "monitoring_config": {
      "enable_performance_monitoring": true,
      "enable_memory_monitoring": true,
      "enable_network_monitoring": true,
      "metrics_retention_days": 7
    }
  },
  
  "ui_config": {
    "accessibility_service": {
      "event_types": ["typeAllMask"],
      "feedback_type": "feedbackGeneric",
      "flags": [
        "flagDefault",
        "flagRetrieveInteractiveWindows",
        "flagReportViewIds",
        "flagRequestTouchExplorationMode"
      ],
      "notification_timeout": 0,
      "can_retrieve_window_content": true,
      "can_request_touch_exploration": true,
      "can_perform_gestures": true
    },
    "operation_delays": {
      "click_delay_ms": 100,
      "input_delay_ms": 200,
      "scroll_delay_ms": 300,
      "swipe_delay_ms": 500
    }
  },
  
  "security_config": {
    "encrypt_api_keys": true,
    "auto_clear_sensitive_data": true,
    "enable_analytics": false,
    "data_retention_days": 30,
    "log_sensitive_data": false
  },
  
  "network_config": {
    "strategy": "wifi_preferred",
    "timeout_seconds": 30,
    "max_retries": 3,
    "retry_delay_ms": 1000,
    "enable_compression": true,
    "user_agent": "DroidRun-HP/1.0.0"
  },
  
  "logging_config": {
    "level": "info",
    "enable_file_logging": true,
    "max_log_files": 5,
    "max_log_size_mb": 10,
    "log_retention_days": 7,
    "enable_crash_reporting": true
  },
  
  "feature_flags": {
    "enable_experimental_features": false,
    "enable_voice_control": false,
    "enable_screenshot_analysis": true,
    "enable_gesture_recording": true,
    "enable_auto_update": true
  },
  
  "knowledge_base": {
    "categories": [
      "android_ui_patterns",
      "common_operations",
      "app_specific_guides",
      "troubleshooting",
      "performance_tips"
    ],
    "auto_update_enabled": true,
    "update_interval_hours": 24,
    "local_cache_enabled": true,
    "max_cache_entries": 1000
  },
  
  "api_server": {
    "enabled": true,
    "port": 8080,
    "host": "0.0.0.0",
    "enable_cors": true,
    "enable_auth": false,
    "rate_limit": {
      "requests_per_minute": 60,
      "burst_size": 10
    }
  }
}
