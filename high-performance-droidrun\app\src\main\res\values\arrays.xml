<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- LLM提供商选项 -->
    <string-array name="llm_providers">
        <item>OpenAI</item>
        <item>Anthropic (<PERSON>)</item>
        <item>Google Gemini</item>
        <item>Ollama (本地)</item>
        <item>自定义</item>
    </string-array>
    
    <string-array name="llm_provider_values">
        <item>openai</item>
        <item>anthropic</item>
        <item>gemini</item>
        <item>ollama</item>
        <item>custom</item>
    </string-array>
    
    <!-- RAG提供商选项 -->
    <string-array name="rag_providers">
        <item>Pinecone</item>
        <item>Weaviate</item>
        <item>Qdrant</item>
        <item>自定义</item>
    </string-array>
    
    <string-array name="rag_provider_values">
        <item>pinecone</item>
        <item>weaviate</item>
        <item>qdrant</item>
        <item>custom</item>
    </string-array>
    
    <!-- OpenAI模型选项 -->
    <string-array name="openai_models">
        <item>GPT-4o</item>
        <item>GPT-4o-mini</item>
        <item>GPT-4-turbo</item>
        <item>GPT-3.5-turbo</item>
    </string-array>
    
    <string-array name="openai_model_values">
        <item>gpt-4o</item>
        <item>gpt-4o-mini</item>
        <item>gpt-4-turbo</item>
        <item>gpt-3.5-turbo</item>
    </string-array>
    
    <!-- Anthropic模型选项 -->
    <string-array name="anthropic_models">
        <item>Claude-3.5-Sonnet</item>
        <item>Claude-3-Haiku</item>
        <item>Claude-3-Opus</item>
    </string-array>
    
    <string-array name="anthropic_model_values">
        <item>claude-3-5-sonnet-20241022</item>
        <item>claude-3-haiku-20240307</item>
        <item>claude-3-opus-20240229</item>
    </string-array>
    
    <!-- Gemini模型选项 -->
    <string-array name="gemini_models">
        <item>Gemini-1.5-Pro</item>
        <item>Gemini-1.5-Flash</item>
        <item>Gemini-1.0-Pro</item>
    </string-array>
    
    <string-array name="gemini_model_values">
        <item>gemini-1.5-pro</item>
        <item>gemini-1.5-flash</item>
        <item>gemini-1.0-pro</item>
    </string-array>
    
    <!-- Ollama模型选项 -->
    <string-array name="ollama_models">
        <item>Llama3.1:8b</item>
        <item>Llama3.1:70b</item>
        <item>Qwen2:7b</item>
        <item>Phi3:mini</item>
        <item>CodeLlama:7b</item>
    </string-array>
    
    <string-array name="ollama_model_values">
        <item>llama3.1:8b</item>
        <item>llama3.1:70b</item>
        <item>qwen2:7b</item>
        <item>phi3:mini</item>
        <item>codellama:7b</item>
    </string-array>
    
    <!-- 任务状态选项 -->
    <string-array name="task_status">
        <item>等待中</item>
        <item>执行中</item>
        <item>已完成</item>
        <item>失败</item>
        <item>已取消</item>
        <item>超时</item>
    </string-array>
    
    <string-array name="task_status_values">
        <item>pending</item>
        <item>running</item>
        <item>completed</item>
        <item>failed</item>
        <item>cancelled</item>
        <item>timeout</item>
    </string-array>
    
    <!-- 日志级别选项 -->
    <string-array name="log_levels">
        <item>详细</item>
        <item>调试</item>
        <item>信息</item>
        <item>警告</item>
        <item>错误</item>
    </string-array>
    
    <string-array name="log_level_values">
        <item>verbose</item>
        <item>debug</item>
        <item>info</item>
        <item>warn</item>
        <item>error</item>
    </string-array>
    
    <!-- 缓存策略选项 -->
    <string-array name="cache_strategies">
        <item>积极缓存</item>
        <item>平衡缓存</item>
        <item>保守缓存</item>
        <item>禁用缓存</item>
    </string-array>
    
    <string-array name="cache_strategy_values">
        <item>aggressive</item>
        <item>balanced</item>
        <item>conservative</item>
        <item>disabled</item>
    </string-array>
    
    <!-- 网络策略选项 -->
    <string-array name="network_strategies">
        <item>仅WiFi</item>
        <item>WiFi优先</item>
        <item>任何网络</item>
        <item>离线模式</item>
    </string-array>
    
    <string-array name="network_strategy_values">
        <item>wifi_only</item>
        <item>wifi_preferred</item>
        <item>any_network</item>
        <item>offline</item>
    </string-array>
    
</resources>
