@echo off
echo Setting up DroidRun Python Version
echo ====================================

echo Step 1: Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ from: https://python.org/downloads/
    pause
    exit /b 1
)

python --version
echo OK: Python found

echo.
echo Step 2: Creating virtual environment...
if exist "venv" (
    echo Virtual environment already exists
) else (
    python -m venv venv
    echo Virtual environment created
)

echo.
echo Step 3: Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Step 4: Installing Python dependencies...
pip install --upgrade pip
pip install kivy kivymd buildozer requests aiohttp sqlite3

echo.
echo Step 5: Creating project structure...
if not exist "src" mkdir src
if not exist "src\ui" mkdir src\ui
if not exist "src\core" mkdir src\core
if not exist "src\ai" mkdir src\ai
if not exist "src\storage" mkdir src\storage
if not exist "src\utils" mkdir src\utils
if not exist "assets" mkdir assets

echo.
echo Step 6: Creating main application file...
echo Creating main.py...

echo.
echo Step 7: Creating buildozer configuration...
echo Creating buildozer.spec...

echo.
echo ====================================
echo SETUP COMPLETE!
echo ====================================
echo.
echo Python version of DroidRun is ready!
echo.
echo Next steps:
echo   1. Activate virtual environment: venv\Scripts\activate
echo   2. Run application: python main.py
echo   3. Build APK: buildozer android debug
echo.
echo Advantages of Python version:
echo   - No Java version issues
echo   - Faster development
echo   - Smaller APK size
echo   - Easier maintenance
echo.
echo To build APK:
echo   buildozer android debug
echo.
echo To install APK:
echo   adb install bin\droidrun-python-debug.apk
echo.
pause
