package com.droidrun.hp.data.model

import android.graphics.Rect
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

/**
 * UI状态数据模型
 */
@Serializable
@Parcelize
data class UIState(
    val timestamp: Long,
    val elements: List<UIElement>,
    val rootNodeId: String,
    val screenWidth: Int = 0,
    val screenHeight: Int = 0,
    val orientation: Int = 0
) : Parcelable

/**
 * UI元素数据模型
 */
@Serializable
@Parcelize
data class UIElement(
    val id: String,
    val className: String,
    val text: String,
    val contentDescription: String,
    val bounds: Rect,
    val isClickable: Boolean,
    val isScrollable: Boolean,
    val isEditable: Boolean,
    val isCheckable: Boolean,
    val isChecked: Boolean,
    val isEnabled: Boolean,
    val isVisible: Boolean,
    val children: List<UIElement> = emptyList()
) : Parcelable

/**
 * UI操作数据模型
 */
@Serializable
@Parcelize
data class UIAction(
    val id: String,
    val type: Type,
    val targetElementId: String,
    val coordinates: Coordinates? = null,
    val endCoordinates: Coordinates? = null,
    val inputText: String? = null,
    val scrollDirection: ScrollDirection? = null,
    val duration: Long? = null,
    val timestamp: Long = System.currentTimeMillis()
) : Parcelable {
    
    @Serializable
    enum class Type {
        CLICK,
        LONG_CLICK,
        DOUBLE_CLICK,
        SCROLL,
        SWIPE,
        INPUT,
        CLEAR,
        SELECT,
        COPY,
        PASTE,
        BACK,
        HOME,
        RECENT_APPS
    }
    
    @Serializable
    enum class ScrollDirection {
        UP,
        DOWN,
        LEFT,
        RIGHT
    }
    
    @Serializable
    @Parcelize
    data class Coordinates(
        val x: Int,
        val y: Int
    ) : Parcelable
}

/**
 * 任务执行结果
 */
@Serializable
@Parcelize
data class TaskResult(
    val taskId: String,
    val command: String,
    val success: Boolean,
    val message: String,
    val startTime: Long,
    val endTime: Long,
    val executedActions: List<UIAction>,
    val finalUIState: UIState? = null,
    val errorDetails: String? = null
) : Parcelable {
    
    val duration: Long
        get() = endTime - startTime
}

/**
 * 知识库条目
 */
@Serializable
@Parcelize
data class KnowledgeEntry(
    val id: String,
    val title: String,
    val content: String,
    val category: String,
    val tags: Set<String> = emptySet(),
    val importance: Float = 1.0f,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable

/**
 * RAG查询
 */
@Serializable
@Parcelize
data class RAGQuery(
    val text: String,
    val category: String? = null,
    val tags: Set<String>? = null,
    val maxResults: Int? = null,
    val similarityThreshold: Float? = null,
    val contextLength: Int? = null
) : Parcelable

/**
 * RAG查询结果
 */
@Serializable
@Parcelize
data class RAGResult(
    val query: String,
    val results: List<RelevantEntry>,
    val context: String,
    val processingTimeMs: Long
) : Parcelable {
    
    @Serializable
    @Parcelize
    data class RelevantEntry(
        val entry: KnowledgeEntry,
        val similarity: Float,
        val relevanceScore: Float
    ) : Parcelable
}

/**
 * Agent执行步骤
 */
@Serializable
@Parcelize
data class AgentStep(
    val stepNumber: Int,
    val type: StepType,
    val thought: String,
    val action: String,
    val parameters: Map<String, String>,
    val result: String,
    val success: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val executionTimeMs: Long = 0
) : Parcelable {
    
    @Serializable
    enum class StepType {
        THINKING,
        ACTION,
        OBSERVATION,
        COMPLETION,
        ERROR
    }
}

/**
 * Agent执行上下文
 */
@Serializable
@Parcelize
data class AgentContext(
    val taskId: String,
    val goal: String,
    val currentStep: Int,
    val maxSteps: Int,
    val history: List<AgentStep>,
    val currentUIState: UIState? = null,
    val memories: List<String> = emptyList(),
    val availableTools: List<String> = emptyList()
) : Parcelable

/**
 * 设备信息
 */
@Serializable
@Parcelize
data class DeviceInfo(
    val brand: String,
    val model: String,
    val androidVersion: String,
    val sdkLevel: Int,
    val screenWidth: Int,
    val screenHeight: Int,
    val density: Float,
    val orientation: Int,
    val isTablet: Boolean = false
) : Parcelable

/**
 * 应用信息
 */
@Serializable
@Parcelize
data class AppInfo(
    val packageName: String,
    val appName: String,
    val versionName: String,
    val versionCode: Long,
    val isSystemApp: Boolean,
    val isEnabled: Boolean,
    val installTime: Long,
    val updateTime: Long
) : Parcelable

/**
 * 性能指标
 */
@Serializable
@Parcelize
data class PerformanceMetrics(
    val timestamp: Long,
    val cpuUsage: Float,
    val memoryUsage: Long,
    val memoryTotal: Long,
    val batteryLevel: Int,
    val temperature: Float,
    val networkLatency: Long = 0,
    val uiResponseTime: Long = 0,
    val llmInferenceTime: Long = 0,
    val ragQueryTime: Long = 0
) : Parcelable {
    
    val memoryUsagePercent: Float
        get() = if (memoryTotal > 0) (memoryUsage.toFloat() / memoryTotal) * 100 else 0f
}

/**
 * 配置设置
 */
@Serializable
@Parcelize
data class AppConfig(
    val llmModelName: String = "llama2-7b-chat",
    val maxSteps: Int = 15,
    val enableVision: Boolean = true,
    val enableRAG: Boolean = true,
    val enablePerformanceMonitoring: Boolean = true,
    val logLevel: LogLevel = LogLevel.INFO,
    val uiUpdateInterval: Long = 100,
    val cacheSize: Int = 1000,
    val maxConcurrentTasks: Int = 3,
    val enableGPUAcceleration: Boolean = true,
    val enableNPUAcceleration: Boolean = false
) : Parcelable {
    
    @Serializable
    enum class LogLevel {
        VERBOSE,
        DEBUG,
        INFO,
        WARN,
        ERROR
    }
}

/**
 * 任务状态
 */
@Serializable
enum class TaskStatus {
    PENDING,
    RUNNING,
    COMPLETED,
    FAILED,
    CANCELLED,
    TIMEOUT
}

/**
 * 任务信息
 */
@Serializable
@Parcelize
data class TaskInfo(
    val id: String,
    val command: String,
    val status: TaskStatus,
    val progress: Float = 0f,
    val currentStep: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val startedAt: Long? = null,
    val completedAt: Long? = null,
    val result: TaskResult? = null
) : Parcelable {
    
    val duration: Long?
        get() = if (startedAt != null && completedAt != null) {
            completedAt - startedAt
        } else null
    
    val isRunning: Boolean
        get() = status == TaskStatus.RUNNING
    
    val isCompleted: Boolean
        get() = status in listOf(TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT)
}

/**
 * API响应基类
 */
@Serializable
@Parcelize
data class ApiResponse<T : Parcelable>(
    val success: Boolean,
    val data: T? = null,
    val message: String = "",
    val errorCode: String? = null,
    val timestamp: Long = System.currentTimeMillis()
) : Parcelable

/**
 * 批量操作结果
 */
@Serializable
@Parcelize
data class BatchResult<T : Parcelable>(
    val totalCount: Int,
    val successCount: Int,
    val failureCount: Int,
    val results: List<T>,
    val errors: List<String> = emptyList()
) : Parcelable {
    
    val successRate: Float
        get() = if (totalCount > 0) successCount.toFloat() / totalCount else 0f
}

/**
 * 缓存条目
 */
@Serializable
@Parcelize
data class CacheEntry<T : Parcelable>(
    val key: String,
    val value: T,
    val createdAt: Long = System.currentTimeMillis(),
    val expiresAt: Long = Long.MAX_VALUE,
    val accessCount: Long = 0,
    val lastAccessAt: Long = System.currentTimeMillis()
) : Parcelable {
    
    val isExpired: Boolean
        get() = System.currentTimeMillis() > expiresAt
    
    val age: Long
        get() = System.currentTimeMillis() - createdAt
}
