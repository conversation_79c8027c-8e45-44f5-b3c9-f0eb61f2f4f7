plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.droidrun.highperformance'
    compileSdk 34

    defaultConfig {
        applicationId "com.droidrun.highperformance"
        minSdk 26  // 支持现代Android特性
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 启用原生库优化
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }

        // 启用向量化指令
        externalNativeBuild {
            cmake {
                cppFlags "-O3 -DNDEBUG -march=native -mfpu=neon"
                arguments "-DANDROID_ARM_NEON=ON", "-DANDROID_STL=c++_shared"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 启用原生代码优化
            ndk {
                debugSymbolLevel 'NONE'
            }
        }
        debug {
            minifyEnabled false
            debuggable true
        }
    }

    // 启用C++支持
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }

    // 启用数据绑定和视图绑定
    buildFeatures {
        dataBinding true
        viewBinding true
        buildConfig true
    }

    // 编译优化
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += [
            "-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-Xopt-in=kotlin.ExperimentalUnsignedTypes",
            "-Xopt-in=kotlinx.serialization.ExperimentalSerializationApi"
        ]
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}

dependencies {
    // Kotlin和协程
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.9.21'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.2'
    
    // Android核心组件
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    
    // 高性能计算和并发
    implementation 'androidx.concurrent:concurrent-futures-ktx:1.1.0'
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    
    // 机器学习和AI
    implementation 'org.tensorflow:tensorflow-lite:2.14.0'
    implementation 'org.tensorflow:tensorflow-lite-gpu:2.14.0'
    implementation 'org.tensorflow:tensorflow-lite-support:0.4.4'
    implementation 'ai.onnxruntime:onnxruntime-android:1.16.3'
    
    // 数据库和存储
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    
    // 网络和序列化
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // 向量计算和数学库
    implementation 'org.apache.commons:commons-math3:3.6.1'
    implementation 'com.github.wendykierp:JTransforms:3.1'
    
    // 性能监控和调试
    implementation 'androidx.benchmark:benchmark-macro-junit4:1.2.2'
    implementation 'androidx.tracing:tracing:1.2.0'
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
    
    // 日志和调试
    implementation 'com.jakewharton.timber:timber:5.0.1'
    
    // 权限管理
    implementation 'com.guolindev.permissionx:permissionx:1.7.1'
    
    // UI组件
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.airbnb.android:lottie:6.2.0'
    
    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.mockito:mockito-core:5.8.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.1'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
}
