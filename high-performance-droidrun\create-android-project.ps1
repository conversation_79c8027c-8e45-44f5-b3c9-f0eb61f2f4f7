# Create Complete Android Project Structure
Write-Host "Creating DroidRun Android Project Structure" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

Write-Host "Step 1: Creating directory structure..." -ForegroundColor Yellow

# Create main directories
$directories = @(
    "app",
    "app\src",
    "app\src\main",
    "app\src\main\java",
    "app\src\main\java\com",
    "app\src\main\java\com\droidrun",
    "app\src\main\java\com\droidrun\hp",
    "app\src\main\res",
    "app\src\main\res\layout",
    "app\src\main\res\values",
    "app\src\main\res\drawable",
    "app\src\main\res\mipmap-hdpi",
    "app\src\main\res\mipmap-mdpi",
    "app\src\main\res\mipmap-xhdpi",
    "app\src\main\res\mipmap-xxhdpi",
    "app\src\main\res\mipmap-xxxhdpi",
    "app\src\androidTest",
    "app\src\androidTest\java",
    "app\src\test",
    "app\src\test\java"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created: $dir" -ForegroundColor Cyan
    }
}

Write-Host "✅ Directory structure created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 2: Creating app/build.gradle..." -ForegroundColor Yellow

$appBuildGradle = @'
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'org.jetbrains.kotlin.plugin.serialization'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
}

android {
    namespace 'com.droidrun.hp'
    compileSdk 34

    defaultConfig {
        applicationId "com.droidrun.hp"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }

    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    // Core Android
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'

    // Serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0'

    // Networking
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'

    // Dependency Injection
    implementation 'com.google.dagger:hilt-android:2.48'
    kapt 'com.google.dagger:hilt-compiler:2.48'

    // Preferences
    implementation 'androidx.datastore:datastore-preferences:1.0.0'

    // Logging
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
}

kapt {
    correctErrorTypes true
}
'@

$appBuildGradle | Out-File -FilePath "app\build.gradle" -Encoding UTF8
Write-Host "✅ app/build.gradle created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 3: Creating AndroidManifest.xml..." -ForegroundColor Yellow

$manifest = @'
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Accessibility service permission -->
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    
    <!-- System overlay permission for floating windows -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    
    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application
        android:name=".DroidRunApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DroidRunHP"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.DroidRunHP">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Settings Activity -->
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity" />

        <!-- Accessibility Service -->
        <service
            android:name=".service.DroidRunAccessibilityService"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- Background Service -->
        <service
            android:name=".service.DroidRunBackgroundService"
            android:exported="false" />

        <!-- API Server Service -->
        <service
            android:name=".service.ApiServerService"
            android:exported="false" />

    </application>

</manifest>
'@

$manifest | Out-File -FilePath "app\src\main\AndroidManifest.xml" -Encoding UTF8
Write-Host "✅ AndroidManifest.xml created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 4: Creating basic resources..." -ForegroundColor Yellow

# Create strings.xml
$strings = @'
<resources>
    <string name="app_name">DroidRun HP</string>
    <string name="app_description">High-Performance Android Automation</string>
    
    <!-- Main Activity -->
    <string name="main_title">DroidRun 高性能版</string>
    <string name="main_subtitle">AI驱动的Android自动化</string>
    
    <!-- Common -->
    <string name="ok">确定</string>
    <string name="cancel">取消</string>
    <string name="settings">设置</string>
    <string name="about">关于</string>
    
    <!-- Accessibility -->
    <string name="accessibility_service_description">DroidRun需要无障碍服务权限来执行UI自动化操作</string>
    <string name="enable_accessibility">启用无障碍服务</string>
    
    <!-- Status -->
    <string name="status_ready">就绪</string>
    <string name="status_running">运行中</string>
    <string name="status_stopped">已停止</string>
    <string name="status_error">错误</string>
</resources>
'@

$strings | Out-File -FilePath "app\src\main\res\values\strings.xml" -Encoding UTF8

# Create colors.xml
$colors = @'
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- DroidRun Colors -->
    <color name="primary">#FF2196F3</color>
    <color name="primary_dark">#FF1976D2</color>
    <color name="accent">#FF4CAF50</color>
    <color name="background">#FFF5F5F5</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="error">#FFF44336</color>
</resources>
'@

$colors | Out-File -FilePath "app\src\main\res\values\colors.xml" -Encoding UTF8

# Create themes.xml
$themes = @'
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.DroidRunHP" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
</resources>
'@

$themes | Out-File -FilePath "app\src\main\res\values\themes.xml" -Encoding UTF8

Write-Host "✅ Basic resources created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 5: Creating main application class..." -ForegroundColor Yellow

$application = @'
package com.droidrun.hp

import android.app.Application
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber

@HiltAndroidApp
class DroidRunApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Timber for logging
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
        
        Timber.i("DroidRun High Performance Application started")
    }
}
'@

$application | Out-File -FilePath "app\src\main\java\com\droidrun\hp\DroidRunApplication.kt" -Encoding UTF8
Write-Host "✅ Application class created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 6: Creating MainActivity..." -ForegroundColor Yellow

$mainActivity = @'
package com.droidrun.hp.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.droidrun.hp.R
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        Timber.i("MainActivity created")
        
        setupUI()
    }
    
    private fun setupUI() {
        // TODO: Setup main UI
        supportActionBar?.title = getString(R.string.main_title)
    }
}
'@

$mainActivity | Out-File -FilePath "app\src\main\java\com\droidrun\hp\ui\MainActivity.kt" -Encoding UTF8

# Create activity_main.xml
$activityMain = @'
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.MainActivity">

    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/main_title"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/subtitleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/main_subtitle"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleText" />

</androidx.constraintlayout.widget.ConstraintLayout>
'@

$activityMain | Out-File -FilePath "app\src\main\res\layout\activity_main.xml" -Encoding UTF8
Write-Host "✅ MainActivity created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 7: Creating additional required files..." -ForegroundColor Yellow

# Create proguard-rules.pro
$proguard = @'
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep Kotlin metadata
-keep class kotlin.Metadata { *; }

# Keep serialization classes
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**

# Timber
-dontwarn org.jetbrains.annotations.**
'@

$proguard | Out-File -FilePath "app\proguard-rules.pro" -Encoding UTF8

# Create accessibility service config
$accessibilityConfig = @'
<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows|flagReportViewIds"
    android:canRetrieveWindowContent="true"
    android:canRequestTouchExplorationMode="true"
    android:canPerformGestures="true"
    android:description="@string/accessibility_service_description"
    android:notificationTimeout="0" />
'@

if (-not (Test-Path "app\src\main\res\xml")) {
    New-Item -ItemType Directory -Path "app\src\main\res\xml" -Force | Out-Null
}
$accessibilityConfig | Out-File -FilePath "app\src\main\res\xml\accessibility_service_config.xml" -Encoding UTF8

Write-Host "✅ Additional files created" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 ========================================" -ForegroundColor Green
Write-Host "🎉 ANDROID PROJECT STRUCTURE CREATED!" -ForegroundColor Green
Write-Host "🎉 ========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Project is now ready for building!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run: .\build-with-existing-java17.ps1" -ForegroundColor White
Write-Host "2. The build should now succeed!" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue"
'@

$createScript | Out-File -FilePath "create-android-project.ps1" -Encoding UTF8
Write-Host "✅ Project creation script saved" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 ========================================" -ForegroundColor Green
Write-Host "🎉 ANDROID PROJECT STRUCTURE CREATED!" -ForegroundColor Green
Write-Host "🎉 ========================================" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Created essential Gradle files:" -ForegroundColor Green
Write-Host "   - settings.gradle" -ForegroundColor Cyan
Write-Host "   - build.gradle (root)" -ForegroundColor Cyan
Write-Host "   - app/build.gradle" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ Created Android project structure:" -ForegroundColor Green
Write-Host "   - AndroidManifest.xml" -ForegroundColor Cyan
Write-Host "   - MainActivity.kt" -ForegroundColor Cyan
Write-Host "   - DroidRunApplication.kt" -ForegroundColor Cyan
Write-Host "   - Resources (strings, colors, themes)" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 Now you can build the project!" -ForegroundColor Yellow
Write-Host ""

Read-Host "Press Enter to exit"
