@echo off
echo DroidRun Build with Java 17
echo ===========================

echo Setting up environment with correct Java version...

REM Check if we have Java 17 installed
if exist "C:\java\jdk-17.0.9+9\bin\java.exe" (
    echo Using downloaded Java 17
    set JAVA_HOME=C:\java\jdk-17.0.9+9
    set PATH=%JAVA_HOME%\bin;%PATH%
) else (
    echo Checking system Java version...
    java -version 2>&1 | find "17" >nul
    if %errorlevel% equ 0 (
        echo OK: System Java 17 found
    ) else (
        java -version 2>&1 | find "11" >nul
        if %errorlevel% equ 0 (
            echo OK: System Java 11 found
        ) else (
            echo ERROR: Java 11+ required
            echo Please run: .\fix-java.bat
            pause
            exit /b 1
        )
    )
)

REM Set Gradle
if exist "C:\gradle\gradle-8.4\bin\gradle.bat" (
    set GRADLE_HOME=C:\gradle\gradle-8.4
    set PATH=%GRADLE_HOME%\bin;%PATH%
    echo Using Gradle 8.4: %GRADLE_HOME%
) else (
    echo ERROR: Gradle not found
    echo Please run: .\install-gradle.bat
    pause
    exit /b 1
)

REM Set Android SDK
if not defined ANDROID_HOME (
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
    ) else if exist "C:\Android\Sdk" (
        set ANDROID_HOME=C:\Android\Sdk
    ) else (
        echo WARNING: Android SDK not found
        echo Build may fail without Android SDK
        echo Please install Android Studio
    )
)

echo.
echo Build environment:
echo   Java: 
java -version
echo   Gradle: 
gradle --version | find "Gradle"
echo   Android SDK: %ANDROID_HOME%
echo.

echo Starting build...
echo =================

echo Cleaning project...
gradle clean

echo Building release APK...
gradle assembleRelease --info

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESS!
    echo ========================================
    echo.
    
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK Location: app\build\outputs\apk\release\app-release.apk
        
        REM Show file size
        for %%A in ("app\build\outputs\apk\release\app-release.apk") do (
            set /a size_mb=%%~zA/1024/1024
            echo APK Size: %%~zA bytes (~!size_mb! MB)
        )
        
        echo.
        echo To install on device:
        echo   adb install app\build\outputs\apk\release\app-release.apk
        echo.
        echo Configuration guide:
        echo   See DOUBAO_SETUP_GUIDE.md for Doubao service setup
        
    ) else (
        echo APK not found in expected location
        echo Searching for APK files...
        for /r %%i in (*.apk) do echo Found: %%i
    )
    
) else (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo Check the error messages above for details.
    echo Common solutions:
    echo   1. Install Android Studio (includes Android SDK)
    echo   2. Check internet connection
    echo   3. Ensure sufficient disk space
)

echo.
pause
