package com.droidrun.hp.config

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.droidrun.hp.data.model.LLMProvider
import com.droidrun.hp.data.model.RAGProvider
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DroidRun配置管理器
 * 管理LLM、RAG和应用的所有配置
 */
@Singleton
class DroidRunConfig @Inject constructor(
    private val context: Context
) {
    
    companion object {
        // DataStore实例
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "droidrun_config")
        
        // LLM配置键
        private val LLM_PROVIDER = stringPreferencesKey("llm_provider")
        private val DEEPSEEK_API_KEY = stringPreferencesKey("deepseek_api_key")
        private val LLM_MODEL = stringPreferencesKey("llm_model")
        private val LLM_TEMPERATURE = floatPreferencesKey("llm_temperature")
        private val LLM_MAX_TOKENS = intPreferencesKey("llm_max_tokens")
        private val LLM_TIMEOUT = longPreferencesKey("llm_timeout")

        // 豆包服务配置键
        private val ARK_API_KEY = stringPreferencesKey("ark_api_key")
        private val VIKING_DB_AK = stringPreferencesKey("viking_db_ak")
        private val VIKING_DB_SK = stringPreferencesKey("viking_db_sk")

        // RAG配置键
        private val RAG_PROVIDER = stringPreferencesKey("rag_provider")
        private val RAG_COLLECTION = stringPreferencesKey("rag_collection")
        private val RAG_TOP_K = intPreferencesKey("rag_top_k")
        private val RAG_SIMILARITY_THRESHOLD = floatPreferencesKey("rag_similarity_threshold")
        private val USE_CLOUD_KB = booleanPreferencesKey("use_cloud_kb")
        
        // 应用配置键
        private val LOCAL_DECISION_THRESHOLD = floatPreferencesKey("local_decision_threshold")
        private val CACHE_SIZE = intPreferencesKey("cache_size")
        private val MAX_CONCURRENT_TASKS = intPreferencesKey("max_concurrent_tasks")
        private val ENABLE_PERFORMANCE_MONITORING = booleanPreferencesKey("enable_performance_monitoring")
        private val ENABLE_DEBUG_LOGGING = booleanPreferencesKey("enable_debug_logging")
        private val AUTO_CLEANUP_DAYS = intPreferencesKey("auto_cleanup_days")
        
        // 默认值 - 基于实际可用服务
        const val DEFAULT_LLM_PROVIDER = "deepseek"
        const val DEFAULT_LLM_MODEL = "deepseek-chat"
        const val DEFAULT_LLM_TEMPERATURE = 0.2f
        const val DEFAULT_LLM_MAX_TOKENS = 512
        const val DEFAULT_LLM_TIMEOUT = 30000L

        const val DEFAULT_RAG_PROVIDER = "viking_db"
        const val DEFAULT_RAG_TOP_K = 5
        const val DEFAULT_RAG_SIMILARITY_THRESHOLD = 0.7f
        
        const val DEFAULT_LOCAL_DECISION_THRESHOLD = 0.8f
        const val DEFAULT_CACHE_SIZE = 100
        const val DEFAULT_MAX_CONCURRENT_TASKS = 3
        const val DEFAULT_AUTO_CLEANUP_DAYS = 30
    }
    
    private val dataStore = context.dataStore
    
    /**
     * LLM配置数据类
     */
    data class LLMConfig(
        val provider: LLMProvider = LLMProvider.DEEPSEEK,
        val deepseekApiKey: String = "",
        val model: String = DEFAULT_LLM_MODEL,
        val temperature: Float = DEFAULT_LLM_TEMPERATURE,
        val maxTokens: Int = DEFAULT_LLM_MAX_TOKENS,
        val timeout: Long = DEFAULT_LLM_TIMEOUT
    )

    /**
     * 豆包服务配置数据类
     */
    data class DoubaoConfig(
        val arkApiKey: String = "",
        val vikingDbAk: String = "",
        val vikingDbSk: String = ""
    )

    /**
     * RAG配置数据类
     */
    data class RAGConfig(
        val provider: RAGProvider = RAGProvider.VIKING_DB,
        val collection: String = "droidRunRag",
        val topK: Int = DEFAULT_RAG_TOP_K,
        val similarityThreshold: Float = DEFAULT_RAG_SIMILARITY_THRESHOLD,
        val useCloudKB: Boolean = true
    )
    
    /**
     * 应用配置数据类
     */
    data class AppConfig(
        val localDecisionThreshold: Float = DEFAULT_LOCAL_DECISION_THRESHOLD,
        val cacheSize: Int = DEFAULT_CACHE_SIZE,
        val maxConcurrentTasks: Int = DEFAULT_MAX_CONCURRENT_TASKS,
        val enablePerformanceMonitoring: Boolean = true,
        val enableDebugLogging: Boolean = false,
        val autoCleanupDays: Int = DEFAULT_AUTO_CLEANUP_DAYS
    )
    
    // ===========================================
    // LLM配置方法
    // ===========================================
    
    /**
     * 获取LLM配置
     */
    val llmConfig: Flow<LLMConfig> = dataStore.data.map { preferences ->
        LLMConfig(
            provider = LLMProvider.valueOf(
                preferences[LLM_PROVIDER] ?: DEFAULT_LLM_PROVIDER.uppercase()
            ),
            apiKey = preferences[LLM_API_KEY] ?: "",
            baseUrl = preferences[LLM_BASE_URL] ?: "",
            model = preferences[LLM_MODEL] ?: DEFAULT_LLM_MODEL,
            temperature = preferences[LLM_TEMPERATURE] ?: DEFAULT_LLM_TEMPERATURE,
            maxTokens = preferences[LLM_MAX_TOKENS] ?: DEFAULT_LLM_MAX_TOKENS,
            timeout = preferences[LLM_TIMEOUT] ?: DEFAULT_LLM_TIMEOUT
        )
    }
    
    /**
     * 更新LLM配置
     */
    suspend fun updateLLMConfig(config: LLMConfig) {
        dataStore.edit { preferences ->
            preferences[LLM_PROVIDER] = config.provider.name.lowercase()
            preferences[LLM_API_KEY] = config.apiKey
            preferences[LLM_BASE_URL] = config.baseUrl
            preferences[LLM_MODEL] = config.model
            preferences[LLM_TEMPERATURE] = config.temperature
            preferences[LLM_MAX_TOKENS] = config.maxTokens
            preferences[LLM_TIMEOUT] = config.timeout
        }
    }
    
    /**
     * 设置LLM API密钥
     */
    suspend fun setLLMApiKey(apiKey: String) {
        dataStore.edit { preferences ->
            preferences[LLM_API_KEY] = apiKey
        }
    }
    
    /**
     * 设置LLM提供商
     */
    suspend fun setLLMProvider(provider: LLMProvider, baseUrl: String = "") {
        dataStore.edit { preferences ->
            preferences[LLM_PROVIDER] = provider.name.lowercase()
            if (baseUrl.isNotEmpty()) {
                preferences[LLM_BASE_URL] = baseUrl
            }
            // 根据提供商设置默认模型
            preferences[LLM_MODEL] = when (provider) {
                LLMProvider.DOUBAO -> "doubao-pro-4k"
                LLMProvider.OPENAI -> "gpt-4o-mini"
                LLMProvider.OLLAMA -> "llama3.1:8b"
                LLMProvider.CUSTOM -> "custom-model"
            }
        }
    }
    
    // ===========================================
    // RAG配置方法
    // ===========================================
    
    /**
     * 获取RAG配置
     */
    val ragConfig: Flow<RAGConfig> = dataStore.data.map { preferences ->
        RAGConfig(
            provider = RAGProvider.valueOf(
                preferences[RAG_PROVIDER] ?: DEFAULT_RAG_PROVIDER.uppercase()
            ),
            apiKey = preferences[RAG_API_KEY] ?: "",
            baseUrl = preferences[RAG_BASE_URL] ?: "",
            indexName = preferences[RAG_INDEX_NAME] ?: "droidrun-knowledge",
            namespace = preferences[RAG_NAMESPACE] ?: "android-automation",
            topK = preferences[RAG_TOP_K] ?: DEFAULT_RAG_TOP_K,
            similarityThreshold = preferences[RAG_SIMILARITY_THRESHOLD] ?: DEFAULT_RAG_SIMILARITY_THRESHOLD
        )
    }
    
    /**
     * 更新RAG配置
     */
    suspend fun updateRAGConfig(config: RAGConfig) {
        dataStore.edit { preferences ->
            preferences[RAG_PROVIDER] = config.provider.name.lowercase()
            preferences[RAG_API_KEY] = config.apiKey
            preferences[RAG_BASE_URL] = config.baseUrl
            preferences[RAG_INDEX_NAME] = config.indexName
            preferences[RAG_NAMESPACE] = config.namespace
            preferences[RAG_TOP_K] = config.topK
            preferences[RAG_SIMILARITY_THRESHOLD] = config.similarityThreshold
        }
    }
    
    /**
     * 设置RAG API密钥
     */
    suspend fun setRAGApiKey(apiKey: String) {
        dataStore.edit { preferences ->
            preferences[RAG_API_KEY] = apiKey
        }
    }
    
    /**
     * 设置RAG提供商
     */
    suspend fun setRAGProvider(provider: RAGProvider, baseUrl: String = "") {
        dataStore.edit { preferences ->
            preferences[RAG_PROVIDER] = provider.name.lowercase()
            if (baseUrl.isNotEmpty()) {
                preferences[RAG_BASE_URL] = baseUrl
            }
            // 根据提供商设置默认配置
            when (provider) {
                RAGProvider.DOUBAO_VECTOR -> {
                    if (baseUrl.isEmpty()) {
                        preferences[RAG_BASE_URL] = "https://ark.cn-beijing.volces.com"
                    }
                }
                RAGProvider.PINECONE -> {
                    if (baseUrl.isEmpty()) {
                        preferences[RAG_BASE_URL] = "https://your-index.pinecone.io"
                    }
                }
                RAGProvider.CUSTOM -> {
                    // 自定义配置
                }
            }
        }
    }
    
    // ===========================================
    // 应用配置方法
    // ===========================================
    
    /**
     * 获取应用配置
     */
    val appConfig: Flow<AppConfig> = dataStore.data.map { preferences ->
        AppConfig(
            localDecisionThreshold = preferences[LOCAL_DECISION_THRESHOLD] ?: DEFAULT_LOCAL_DECISION_THRESHOLD,
            cacheSize = preferences[CACHE_SIZE] ?: DEFAULT_CACHE_SIZE,
            maxConcurrentTasks = preferences[MAX_CONCURRENT_TASKS] ?: DEFAULT_MAX_CONCURRENT_TASKS,
            enablePerformanceMonitoring = preferences[ENABLE_PERFORMANCE_MONITORING] ?: true,
            enableDebugLogging = preferences[ENABLE_DEBUG_LOGGING] ?: false,
            autoCleanupDays = preferences[AUTO_CLEANUP_DAYS] ?: DEFAULT_AUTO_CLEANUP_DAYS
        )
    }
    
    /**
     * 更新应用配置
     */
    suspend fun updateAppConfig(config: AppConfig) {
        dataStore.edit { preferences ->
            preferences[LOCAL_DECISION_THRESHOLD] = config.localDecisionThreshold
            preferences[CACHE_SIZE] = config.cacheSize
            preferences[MAX_CONCURRENT_TASKS] = config.maxConcurrentTasks
            preferences[ENABLE_PERFORMANCE_MONITORING] = config.enablePerformanceMonitoring
            preferences[ENABLE_DEBUG_LOGGING] = config.enableDebugLogging
            preferences[AUTO_CLEANUP_DAYS] = config.autoCleanupDays
        }
    }
    
    // ===========================================
    // 便捷方法
    // ===========================================
    
    /**
     * 检查配置是否完整
     */
    suspend fun isConfigurationComplete(): Flow<Boolean> = dataStore.data.map { preferences ->
        val hasLLMKey = !preferences[LLM_API_KEY].isNullOrEmpty()
        val hasRAGKey = !preferences[RAG_API_KEY].isNullOrEmpty()
        hasLLMKey // RAG是可选的，只要有LLM配置就可以工作
    }
    
    /**
     * 重置为默认配置
     */
    suspend fun resetToDefaults() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }
    
    /**
     * 导出配置
     */
    suspend fun exportConfig(): Map<String, Any> {
        return dataStore.data.map { preferences ->
            preferences.asMap().mapKeys { it.key.name }
        }.collect { it }
    }
    
    /**
     * 导入配置
     */
    suspend fun importConfig(config: Map<String, Any>) {
        dataStore.edit { preferences ->
            config.forEach { (key, value) ->
                when (value) {
                    is String -> preferences[stringPreferencesKey(key)] = value
                    is Int -> preferences[intPreferencesKey(key)] = value
                    is Float -> preferences[floatPreferencesKey(key)] = value
                    is Boolean -> preferences[booleanPreferencesKey(key)] = value
                    is Long -> preferences[longPreferencesKey(key)] = value
                }
            }
        }
    }
}
