package com.droidrun.hp.data.cache

import com.droidrun.hp.data.dao.CacheDao
import com.droidrun.hp.data.entity.CacheEntity
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 智能缓存管理器
 * 提供多层缓存策略，优化性能和内存使用
 */
@Singleton
class IntelligentCacheManager @Inject constructor(
    private val cacheDao: CacheDao
) {
    
    companion object {
        private const val MEMORY_CACHE_SIZE = 100
        private const val CLEANUP_INTERVAL_MS = 300000L // 5分钟
        private const val MAX_CACHE_SIZE_MB = 50 // 最大缓存50MB
        private const val DEFAULT_TTL_MS = 3600000L // 1小时
    }
    
    // 内存缓存（L1缓存）
    private val memoryCache = ConcurrentHashMap<String, CacheItem>()
    
    // 缓存统计
    private val hitCount = AtomicLong(0)
    private val missCount = AtomicLong(0)
    private val totalSize = AtomicLong(0)
    
    // 清理任务
    private var cleanupJob: Job? = null
    private val json = Json { ignoreUnknownKeys = true }
    
    /**
     * 缓存项
     */
    private data class CacheItem(
        val value: String,
        val type: String,
        val createdAt: Long,
        val expiresAt: Long,
        val size: Long,
        var accessCount: Long = 0,
        var lastAccessAt: Long = System.currentTimeMillis()
    ) {
        fun isExpired(): Boolean = System.currentTimeMillis() > expiresAt
        
        fun updateAccess() {
            accessCount++
            lastAccessAt = System.currentTimeMillis()
        }
    }
    
    /**
     * 缓存类型
     */
    enum class CacheType(val typeName: String, val defaultTTL: Long) {
        DECISION("decision", 1800000L),      // 决策缓存 - 30分钟
        LLM_RESPONSE("llm_response", 3600000L), // LLM响应 - 1小时
        RAG_RESULT("rag_result", 1800000L),     // RAG结果 - 30分钟
        UI_STATE("ui_state", 300000L),          // UI状态 - 5分钟
        APP_INFO("app_info", 86400000L),        // 应用信息 - 24小时
        CONFIG("config", Long.MAX_VALUE),       // 配置 - 永不过期
        PERFORMANCE("performance", 3600000L)    // 性能数据 - 1小时
    }
    
    /**
     * 启动缓存管理器
     */
    fun start() {
        if (cleanupJob?.isActive == true) return
        
        cleanupJob = CoroutineScope(Dispatchers.IO).launch {
            while (isActive) {
                try {
                    performCleanup()
                    delay(CLEANUP_INTERVAL_MS)
                } catch (e: Exception) {
                    Timber.e(e, "缓存清理异常")
                }
            }
        }
        
        Timber.i("智能缓存管理器已启动")
    }
    
    /**
     * 停止缓存管理器
     */
    fun stop() {
        cleanupJob?.cancel()
        cleanupJob = null
        Timber.i("智能缓存管理器已停止")
    }
    
    /**
     * 存储缓存
     */
    suspend fun <T> put(
        key: String,
        value: T,
        type: CacheType,
        ttl: Long = type.defaultTTL
    ): Boolean {
        return PerformanceProfiler.measureExecutionTimeAsync("Cache.Put") {
            try {
                val valueJson = json.encodeToString(value)
                val size = valueJson.toByteArray().size.toLong()
                val expiresAt = System.currentTimeMillis() + ttl
                
                // 存储到内存缓存
                val cacheItem = CacheItem(
                    value = valueJson,
                    type = type.typeName,
                    createdAt = System.currentTimeMillis(),
                    expiresAt = expiresAt,
                    size = size
                )
                
                // 检查内存缓存容量
                if (memoryCache.size >= MEMORY_CACHE_SIZE) {
                    evictLeastRecentlyUsed()
                }
                
                memoryCache[key] = cacheItem
                totalSize.addAndGet(size)
                
                // 异步存储到数据库（L2缓存）
                withContext(Dispatchers.IO) {
                    val entity = CacheEntity(
                        key = key,
                        value = valueJson,
                        type = type.typeName,
                        expiresAt = expiresAt,
                        size = size
                    )
                    cacheDao.insertCache(entity)
                }
                
                Timber.d("缓存存储: $key (${type.typeName}, ${size}B)")
                true
            } catch (e: Exception) {
                Timber.e(e, "缓存存储失败: $key")
                false
            }
        }
    }
    
    /**
     * 获取缓存
     */
    suspend inline fun <reified T> get(key: String, type: CacheType): T? {
        return PerformanceProfiler.measureExecutionTimeAsync("Cache.Get") {
            try {
                // 先检查内存缓存
                val memoryItem = memoryCache[key]
                if (memoryItem != null && !memoryItem.isExpired()) {
                    memoryItem.updateAccess()
                    hitCount.incrementAndGet()
                    Timber.d("内存缓存命中: $key")
                    return@measureExecutionTimeAsync json.decodeFromString<T>(memoryItem.value)
                }
                
                // 检查数据库缓存
                val entity = withContext(Dispatchers.IO) {
                    cacheDao.getCacheByKey(key)
                }
                
                if (entity != null && entity.expiresAt > System.currentTimeMillis()) {
                    // 更新访问统计
                    withContext(Dispatchers.IO) {
                        cacheDao.updateCacheAccess(key, System.currentTimeMillis())
                    }
                    
                    // 加载到内存缓存
                    val cacheItem = CacheItem(
                        value = entity.value,
                        type = entity.type,
                        createdAt = entity.createdAt,
                        expiresAt = entity.expiresAt,
                        size = entity.size,
                        accessCount = entity.accessCount + 1,
                        lastAccessAt = System.currentTimeMillis()
                    )
                    
                    if (memoryCache.size >= MEMORY_CACHE_SIZE) {
                        evictLeastRecentlyUsed()
                    }
                    memoryCache[key] = cacheItem
                    
                    hitCount.incrementAndGet()
                    Timber.d("数据库缓存命中: $key")
                    return@measureExecutionTimeAsync json.decodeFromString<T>(entity.value)
                }
                
                missCount.incrementAndGet()
                Timber.d("缓存未命中: $key")
                null
            } catch (e: Exception) {
                Timber.e(e, "缓存获取失败: $key")
                missCount.incrementAndGet()
                null
            }
        }
    }
    
    /**
     * 删除缓存
     */
    suspend fun remove(key: String): Boolean {
        return try {
            memoryCache.remove(key)?.let { item ->
                totalSize.addAndGet(-item.size)
            }
            
            withContext(Dispatchers.IO) {
                cacheDao.deleteCache(key)
            }
            
            Timber.d("缓存删除: $key")
            true
        } catch (e: Exception) {
            Timber.e(e, "缓存删除失败: $key")
            false
        }
    }
    
    /**
     * 清空指定类型的缓存
     */
    suspend fun clearByType(type: CacheType): Int {
        return try {
            // 清理内存缓存
            val memoryKeys = memoryCache.keys.filter { key ->
                memoryCache[key]?.type == type.typeName
            }
            memoryKeys.forEach { key ->
                memoryCache.remove(key)?.let { item ->
                    totalSize.addAndGet(-item.size)
                }
            }
            
            // 清理数据库缓存
            val deletedCount = withContext(Dispatchers.IO) {
                cacheDao.deleteCacheByType(type.typeName)
            }
            
            Timber.i("清理缓存类型: ${type.typeName}, 删除 $deletedCount 项")
            deletedCount
        } catch (e: Exception) {
            Timber.e(e, "清理缓存类型失败: ${type.typeName}")
            0
        }
    }
    
    /**
     * 清空所有缓存
     */
    suspend fun clearAll(): Boolean {
        return try {
            memoryCache.clear()
            totalSize.set(0)
            
            withContext(Dispatchers.IO) {
                cacheDao.deleteAllCache()
            }
            
            Timber.i("清空所有缓存")
            true
        } catch (e: Exception) {
            Timber.e(e, "清空缓存失败")
            false
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): CacheStats {
        val totalRequests = hitCount.get() + missCount.get()
        val hitRate = if (totalRequests > 0) {
            (hitCount.get().toFloat() / totalRequests) * 100
        } else 0f
        
        return CacheStats(
            memoryItems = memoryCache.size,
            totalSizeBytes = totalSize.get(),
            hitCount = hitCount.get(),
            missCount = missCount.get(),
            hitRate = hitRate
        )
    }
    
    /**
     * 执行清理操作
     */
    private suspend fun performCleanup() {
        try {
            // 清理过期的内存缓存
            val expiredKeys = memoryCache.entries.filter { (_, item) ->
                item.isExpired()
            }.map { it.key }
            
            expiredKeys.forEach { key ->
                memoryCache.remove(key)?.let { item ->
                    totalSize.addAndGet(-item.size)
                }
            }
            
            // 清理过期的数据库缓存
            val deletedCount = withContext(Dispatchers.IO) {
                cacheDao.deleteExpiredCache(System.currentTimeMillis())
            }
            
            // 检查缓存大小，如果超过限制则清理最少使用的项
            if (totalSize.get() > MAX_CACHE_SIZE_MB * 1024 * 1024) {
                evictLeastRecentlyUsed()
            }
            
            if (expiredKeys.isNotEmpty() || deletedCount > 0) {
                Timber.d("缓存清理完成: 内存 ${expiredKeys.size} 项, 数据库 $deletedCount 项")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "缓存清理异常")
        }
    }
    
    /**
     * 驱逐最少使用的缓存项
     */
    private fun evictLeastRecentlyUsed() {
        if (memoryCache.isEmpty()) return
        
        val lruKey = memoryCache.entries.minByOrNull { (_, item) ->
            item.lastAccessAt
        }?.key
        
        lruKey?.let { key ->
            memoryCache.remove(key)?.let { item ->
                totalSize.addAndGet(-item.size)
                Timber.d("驱逐LRU缓存: $key")
            }
        }
    }
}

/**
 * 缓存统计信息
 */
data class CacheStats(
    val memoryItems: Int,
    val totalSizeBytes: Long,
    val hitCount: Long,
    val missCount: Long,
    val hitRate: Float
) {
    val totalSizeMB: Float
        get() = totalSizeBytes / (1024f * 1024f)
}
