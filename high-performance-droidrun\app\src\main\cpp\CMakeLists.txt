cmake_minimum_required(VERSION 3.22.1)

project("droidrun_hp")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译优化选项
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -ffast-math -march=native")
set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG -ffast-math -march=native")

# 启用NEON指令集（ARM）
if(ANDROID_ABI STREQUAL "arm64-v8a" OR ANDROID_ABI STREQUAL "armeabi-v7a")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mfpu=neon")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -mfpu=neon")
endif()

# 查找必要的库
find_library(log-lib log)
find_library(android-lib android)
find_library(jnigraphics-lib jnigraphics)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/llm
    ${CMAKE_CURRENT_SOURCE_DIR}/vector
    ${CMAKE_CURRENT_SOURCE_DIR}/ui
    ${CMAKE_CURRENT_SOURCE_DIR}/utils
)

# LLM推理引擎源文件
set(LLM_SOURCES
    llm/llama_cpp_wrapper.cpp
    llm/onnx_runtime_wrapper.cpp
    llm/inference_engine.cpp
    llm/model_manager.cpp
)

# 向量计算引擎源文件
set(VECTOR_SOURCES
    vector/faiss_wrapper.cpp
    vector/embedding_engine.cpp
    vector/vector_database.cpp
)

# UI分析器源文件
set(UI_SOURCES
    ui/ui_analyzer.cpp
    ui/action_executor.cpp
    ui/ui_cache.cpp
)

# 工具类源文件
set(UTILS_SOURCES
    utils/performance_profiler.cpp
    utils/memory_optimizer.cpp
    utils/benchmark_utils.cpp
)

# JNI桥接源文件
set(JNI_SOURCES
    jni/droidrun_jni.cpp
    jni/llm_jni.cpp
    jni/vector_jni.cpp
    jni/ui_jni.cpp
)

# 创建共享库
add_library(
    droidrun_hp
    SHARED
    ${LLM_SOURCES}
    ${VECTOR_SOURCES}
    ${UI_SOURCES}
    ${UTILS_SOURCES}
    ${JNI_SOURCES}
)

# 链接库
target_link_libraries(
    droidrun_hp
    ${log-lib}
    ${android-lib}
    ${jnigraphics-lib}
)

# 如果使用ONNX Runtime
if(USE_ONNX_RUNTIME)
    # 添加ONNX Runtime预编译库
    add_library(onnxruntime SHARED IMPORTED)
    set_target_properties(onnxruntime PROPERTIES
        IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libonnxruntime.so
    )
    target_link_libraries(droidrun_hp onnxruntime)
    target_compile_definitions(droidrun_hp PRIVATE USE_ONNX_RUNTIME)
endif()

# 如果使用llama.cpp
if(USE_LLAMA_CPP)
    # 添加llama.cpp源文件或预编译库
    add_library(llamacpp SHARED IMPORTED)
    set_target_properties(llamacpp PROPERTIES
        IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libllamacpp.so
    )
    target_link_libraries(droidrun_hp llamacpp)
    target_compile_definitions(droidrun_hp PRIVATE USE_LLAMA_CPP)
endif()

# 如果使用FAISS
if(USE_FAISS)
    add_library(faiss SHARED IMPORTED)
    set_target_properties(faiss PROPERTIES
        IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libfaiss.so
    )
    target_link_libraries(droidrun_hp faiss)
    target_compile_definitions(droidrun_hp PRIVATE USE_FAISS)
endif()

# 编译定义
target_compile_definitions(droidrun_hp PRIVATE
    ANDROID_PLATFORM
    LOG_TAG="DroidRunHP"
)

# 优化选项
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(droidrun_hp PRIVATE
        -fvisibility=hidden
        -ffunction-sections
        -fdata-sections
    )
    target_link_options(droidrun_hp PRIVATE
        -Wl,--gc-sections
        -Wl,--strip-all
    )
endif()
