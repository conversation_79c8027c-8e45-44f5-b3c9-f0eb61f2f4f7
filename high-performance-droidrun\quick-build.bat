@echo off
echo DroidRun Quick Build
echo ===================

echo Checking Java...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not installed
    pause
    exit /b 1
)

echo Cleaning project...
call gradlew.bat clean

echo Building release APK...
call gradlew.bat assembleRelease

if %errorlevel% equ 0 (
    echo.
    echo BUILD SUCCESS!
    echo APK: app\build\outputs\apk\release\app-release.apk
) else (
    echo.
    echo BUILD FAILED
)

pause
