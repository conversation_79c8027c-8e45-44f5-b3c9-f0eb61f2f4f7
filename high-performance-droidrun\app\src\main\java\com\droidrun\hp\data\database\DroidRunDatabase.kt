package com.droidrun.hp.data.database

import androidx.room.*
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.droidrun.hp.data.dao.*
import com.droidrun.hp.data.entity.*
import kotlinx.coroutines.CoroutineScope

/**
 * DroidRun轻量级数据库
 * 使用Room进行本地数据管理
 */
@Database(
    entities = [
        TaskEntity::class,
        ActionHistoryEntity::class,
        UIStateEntity::class,
        CacheEntity::class,
        ConfigEntity::class,
        PerformanceMetricEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class DroidRunDatabase : RoomDatabase() {
    
    abstract fun taskDao(): TaskDao
    abstract fun actionHistoryDao(): ActionHistoryDao
    abstract fun uiStateDao(): UIStateDao
    abstract fun cacheDao(): CacheDao
    abstract fun configDao(): ConfigDao
    abstract fun performanceDao(): PerformanceDao
    
    companion object {
        const val DATABASE_NAME = "droidrun_database"
        
        // 数据库迁移
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 未来版本的迁移逻辑
            }
        }
    }
}

/**
 * 类型转换器
 */
class Converters {
    
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return value.joinToString(",")
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        return if (value.isEmpty()) emptyList() else value.split(",")
    }
    
    @TypeConverter
    fun fromStringMap(value: Map<String, String>): String {
        return value.entries.joinToString(";") { "${it.key}:${it.value}" }
    }
    
    @TypeConverter
    fun toStringMap(value: String): Map<String, String> {
        return if (value.isEmpty()) {
            emptyMap()
        } else {
            value.split(";").associate { pair ->
                val (key, value) = pair.split(":", limit = 2)
                key to value
            }
        }
    }
    
    @TypeConverter
    fun fromTaskStatus(status: com.droidrun.hp.data.model.TaskStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toTaskStatus(status: String): com.droidrun.hp.data.model.TaskStatus {
        return com.droidrun.hp.data.model.TaskStatus.valueOf(status)
    }
    
    @TypeConverter
    fun fromActionType(type: com.droidrun.hp.data.model.UIAction.Type): String {
        return type.name
    }
    
    @TypeConverter
    fun toActionType(type: String): com.droidrun.hp.data.model.UIAction.Type {
        return com.droidrun.hp.data.model.UIAction.Type.valueOf(type)
    }
}
