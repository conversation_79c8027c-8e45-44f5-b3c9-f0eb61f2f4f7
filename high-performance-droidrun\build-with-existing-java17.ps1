# Build with Existing Java 17
Write-Host "DroidRun Build with Existing Java 17" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

Write-Host "Step 1: Locating Java 17 installation..." -ForegroundColor Yellow

# Common Java 17 installation paths
$java17Paths = @(
    "C:\java\jdk-17.0.9+9",
    "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
    "C:\Program Files\Java\jdk-17*",
    "C:\Program Files\Eclipse Adoptium\jdk-17*",
    "C:\Program Files (x86)\Eclipse Adoptium\jdk-17*"
)

$javaFound = $false
$javaPath = ""

foreach ($path in $java17Paths) {
    if ($path -like "*\*") {
        # Handle wildcard paths
        $parentDir = Split-Path $path -Parent
        $pattern = Split-Path $path -Leaf
        if (Test-Path $parentDir) {
            $matchingDirs = Get-ChildItem -Path $parentDir -Directory -Filter $pattern -ErrorAction SilentlyContinue
            if ($matchingDirs) {
                $javaPath = $matchingDirs[0].FullName
                $javaFound = $true
                break
            }
        }
    } else {
        if (Test-Path $path) {
            $javaPath = $path
            $javaFound = $true
            break
        }
    }
}

if ($javaFound) {
    Write-Host "Found Java 17 at: $javaPath" -ForegroundColor Green
    $env:JAVA_HOME = $javaPath
    $env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
} else {
    Write-Host "Java 17 not found in common locations." -ForegroundColor Red
    Write-Host "Please specify the Java 17 installation path:" -ForegroundColor Yellow
    $customPath = Read-Host "Enter Java 17 path (or press Enter to search manually)"
    
    if ($customPath -and (Test-Path $customPath)) {
        $env:JAVA_HOME = $customPath
        $env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
        Write-Host "Using custom Java path: $customPath" -ForegroundColor Green
    } else {
        Write-Host "Searching for Java installations..." -ForegroundColor Yellow
        $javaInstalls = Get-ChildItem -Path "C:\Program Files" -Directory -Filter "*jdk*" -ErrorAction SilentlyContinue
        $javaInstalls += Get-ChildItem -Path "C:\Program Files (x86)" -Directory -Filter "*jdk*" -ErrorAction SilentlyContinue
        $javaInstalls += Get-ChildItem -Path "C:\" -Directory -Filter "*java*" -ErrorAction SilentlyContinue
        
        if ($javaInstalls) {
            Write-Host "Found Java installations:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $javaInstalls.Count; $i++) {
                Write-Host "  $($i + 1). $($javaInstalls[$i].FullName)" -ForegroundColor White
            }
            
            $choice = Read-Host "Select Java installation (1-$($javaInstalls.Count)) or 0 to exit"
            $choiceNum = [int]$choice
            
            if ($choiceNum -gt 0 -and $choiceNum -le $javaInstalls.Count) {
                $env:JAVA_HOME = $javaInstalls[$choiceNum - 1].FullName
                $env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
                Write-Host "Using: $env:JAVA_HOME" -ForegroundColor Green
            } else {
                Write-Host "Invalid choice or exit requested." -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host "No Java installations found. Please install Java 17." -ForegroundColor Red
            exit 1
        }
    }
}

Write-Host ""
Write-Host "Step 2: Verifying Java 17..." -ForegroundColor Yellow
try {
    $javaVersion = & "$env:JAVA_HOME\bin\java.exe" -version 2>&1
    Write-Host $javaVersion -ForegroundColor Cyan
    
    # Check if it's Java 17
    if ($javaVersion -match "17\.") {
        Write-Host "✅ Java 17 confirmed!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Warning: This doesn't appear to be Java 17" -ForegroundColor Yellow
        $continue = Read-Host "Continue anyway? (y/n)"
        if ($continue -ne "y") {
            exit 1
        }
    }
} catch {
    Write-Host "❌ Failed to verify Java installation" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Step 3: Setting up Gradle..." -ForegroundColor Yellow
$env:GRADLE_HOME = "C:\gradle\gradle-8.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"

$gradlePath = "$env:GRADLE_HOME\bin\gradle.bat"
if (-not (Test-Path $gradlePath)) {
    Write-Host "❌ Gradle not found at: $env:GRADLE_HOME" -ForegroundColor Red
    Write-Host "Please run: .\install-gradle.bat" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Gradle found" -ForegroundColor Green

Write-Host ""
Write-Host "Step 4: Creating correct gradle.properties..." -ForegroundColor Yellow

# Create gradle.properties with correct Java 17 path (using forward slashes)
$javaHomeForGradle = $env:JAVA_HOME -replace '\\', '/'
$gradleProperties = @"
# Gradle configuration for DroidRun with Java 17
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
org.gradle.java.home=$javaHomeForGradle
"@

$gradleProperties | Out-File -FilePath "gradle.properties" -Encoding UTF8
Write-Host "✅ gradle.properties created with Java 17 path: $javaHomeForGradle" -ForegroundColor Green

Write-Host ""
Write-Host "Step 5: Setting up Android SDK..." -ForegroundColor Yellow
$androidSdkPaths = @(
    "$env:USERPROFILE\AppData\Local\Android\Sdk",
    "C:\Android\Sdk",
    "C:\Users\<USER>\AppData\Local\Android\Sdk"
)

$androidSdkFound = $false
foreach ($path in $androidSdkPaths) {
    if (Test-Path $path) {
        $env:ANDROID_HOME = $path
        Write-Host "✅ Found Android SDK: $path" -ForegroundColor Green
        $androidSdkFound = $true
        break
    }
}

if (-not $androidSdkFound) {
    Write-Host "⚠️ Android SDK not found" -ForegroundColor Yellow
    Write-Host "Build may fail without Android SDK" -ForegroundColor Yellow
    Write-Host "Please install Android Studio or set ANDROID_HOME manually" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Step 6: Environment Summary..." -ForegroundColor Yellow
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan
Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor Cyan

Write-Host ""
Write-Host "Step 7: Starting build process..." -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

Write-Host "Cleaning project..." -ForegroundColor Cyan
try {
    & $gradlePath clean --no-daemon
    Write-Host "✅ Clean completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Clean failed, but continuing..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Building release APK..." -ForegroundColor Cyan
Write-Host "This may take 5-10 minutes for first build..." -ForegroundColor Yellow

try {
    & $gradlePath assembleRelease --no-daemon --info
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 ========================================" -ForegroundColor Green
        Write-Host "🎉 BUILD SUCCESS!" -ForegroundColor Green
        Write-Host "🎉 ========================================" -ForegroundColor Green
        Write-Host ""
        
        # Find and display APK info
        $apkPath = "app\build\outputs\apk\release\app-release.apk"
        if (Test-Path $apkPath) {
            $apkFile = Get-Item $apkPath
            $sizeMB = [math]::Round($apkFile.Length / 1MB, 2)
            
            Write-Host "📱 APK created successfully!" -ForegroundColor Green
            Write-Host "📍 Location: $apkPath" -ForegroundColor Cyan
            Write-Host "📦 Size: $($apkFile.Length) bytes (~$sizeMB MB)" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "🔧 Installation command:" -ForegroundColor Yellow
            Write-Host "   adb install `"$apkPath`"" -ForegroundColor White
            Write-Host ""
            Write-Host "📋 Next steps:" -ForegroundColor Yellow
            Write-Host "   1. Connect Android device via USB" -ForegroundColor White
            Write-Host "   2. Enable USB debugging in Developer Options" -ForegroundColor White
            Write-Host "   3. Run: adb install `"$apkPath`"" -ForegroundColor White
            Write-Host "   4. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)" -ForegroundColor White
            Write-Host "   5. Enable accessibility service in Android settings" -ForegroundColor White
            Write-Host ""
            Write-Host "🎯 Your DroidRun high-performance app is ready!" -ForegroundColor Green
            
        } else {
            Write-Host "⚠️ APK not found in expected location, searching..." -ForegroundColor Yellow
            Get-ChildItem -Recurse -Filter "*.apk" | ForEach-Object {
                Write-Host "Found APK: $($_.FullName)" -ForegroundColor Cyan
            }
        }
        
    } else {
        Write-Host ""
        Write-Host "❌ ========================================" -ForegroundColor Red
        Write-Host "❌ BUILD FAILED" -ForegroundColor Red
        Write-Host "❌ ========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "Common causes and solutions:" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "1. Missing Android SDK:" -ForegroundColor White
        Write-Host "   - Download Android Studio: https://developer.android.com/studio" -ForegroundColor Gray
        Write-Host "   - Install Android SDK through Android Studio" -ForegroundColor Gray
        Write-Host ""
        Write-Host "2. Network issues:" -ForegroundColor White
        Write-Host "   - Check internet connection" -ForegroundColor Gray
        Write-Host "   - Try again (dependencies may still be downloading)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "3. Disk space:" -ForegroundColor White
        Write-Host "   - Ensure at least 5GB free space" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Check the detailed error messages above for specific issues." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Build failed with exception: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Build process completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
