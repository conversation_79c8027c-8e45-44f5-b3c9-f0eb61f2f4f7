package com.droidrun.hp.utils

import android.os.SystemClock
import android.os.Trace
import androidx.tracing.trace
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

/**
 * 高性能分析器
 * 提供详细的性能监控和分析功能
 */
object PerformanceProfiler {
    
    private const val TRACE_TAG = "DroidRunHP"
    private const val MAX_METRICS_HISTORY = 1000
    
    // 性能指标存储
    private val metricsHistory = ConcurrentHashMap<String, MutableList<PerformanceMetric>>()
    private val operationCounters = ConcurrentHashMap<String, AtomicLong>()
    private val totalExecutionTimes = ConcurrentHashMap<String, AtomicLong>()
    
    // 实时监控
    private var isMonitoring = false
    private var monitoringJob: Job? = null
    
    /**
     * 性能指标数据类
     */
    data class PerformanceMetric(
        val operation: String,
        val executionTime: Long,
        val timestamp: Long = System.currentTimeMillis(),
        val threadName: String = Thread.currentThread().name,
        val memoryUsage: Long = getMemoryUsage(),
        val cpuUsage: Float = getCpuUsage()
    )
    
    /**
     * 性能统计数据类
     */
    data class PerformanceStats(
        val operation: String,
        val totalExecutions: Long,
        val totalTime: Long,
        val averageTime: Double,
        val minTime: Long,
        val maxTime: Long,
        val p50Time: Long,
        val p95Time: Long,
        val p99Time: Long,
        val throughput: Double // 每秒操作数
    )
    
    /**
     * 测量操作执行时间
     */
    inline fun <T> measureExecutionTime(
        operation: String,
        enableTracing: Boolean = true,
        block: () -> T
    ): T {
        val startTime = SystemClock.elapsedRealtimeNanos()
        val startMemory = getMemoryUsage()
        
        return if (enableTracing) {
            trace("$TRACE_TAG:$operation") {
                try {
                    block()
                } finally {
                    recordMetric(operation, startTime, startMemory)
                }
            }
        } else {
            try {
                block()
            } finally {
                recordMetric(operation, startTime, startMemory)
            }
        }
    }
    
    /**
     * 异步测量操作执行时间
     */
    suspend inline fun <T> measureExecutionTimeAsync(
        operation: String,
        enableTracing: Boolean = true,
        crossinline block: suspend () -> T
    ): T {
        val startTime = SystemClock.elapsedRealtimeNanos()
        val startMemory = getMemoryUsage()
        
        return if (enableTracing) {
            trace("$TRACE_TAG:$operation") {
                try {
                    block()
                } finally {
                    recordMetric(operation, startTime, startMemory)
                }
            }
        } else {
            try {
                block()
            } finally {
                recordMetric(operation, startTime, startMemory)
            }
        }
    }
    
    /**
     * 记录性能指标
     */
    private fun recordMetric(operation: String, startTime: Long, startMemory: Long) {
        val executionTime = (SystemClock.elapsedRealtimeNanos() - startTime) / 1_000_000 // 转换为毫秒
        val memoryDelta = getMemoryUsage() - startMemory
        
        val metric = PerformanceMetric(
            operation = operation,
            executionTime = executionTime,
            memoryUsage = memoryDelta
        )
        
        // 更新计数器
        operationCounters.computeIfAbsent(operation) { AtomicLong(0) }.incrementAndGet()
        totalExecutionTimes.computeIfAbsent(operation) { AtomicLong(0) }.addAndGet(executionTime)
        
        // 存储历史记录
        metricsHistory.computeIfAbsent(operation) { mutableListOf() }.apply {
            synchronized(this) {
                add(metric)
                // 保持历史记录在限制范围内
                if (size > MAX_METRICS_HISTORY) {
                    removeAt(0)
                }
            }
        }
        
        // 记录日志
        Timber.d("[$operation] 执行时间: ${executionTime}ms, 内存变化: ${memoryDelta}KB")
        
        // 如果执行时间过长，记录警告
        if (executionTime > 1000) {
            Timber.w("[$operation] 执行时间过长: ${executionTime}ms")
        }
    }
    
    /**
     * 获取操作的性能统计
     */
    fun getPerformanceStats(operation: String): PerformanceStats? {
        val metrics = metricsHistory[operation] ?: return null
        val counter = operationCounters[operation] ?: return null
        val totalTime = totalExecutionTimes[operation] ?: return null
        
        if (metrics.isEmpty()) return null
        
        val executionTimes = metrics.map { it.executionTime }.sorted()
        val totalExecutions = counter.get()
        val averageTime = totalTime.get().toDouble() / totalExecutions
        
        return PerformanceStats(
            operation = operation,
            totalExecutions = totalExecutions,
            totalTime = totalTime.get(),
            averageTime = averageTime,
            minTime = executionTimes.minOrNull() ?: 0,
            maxTime = executionTimes.maxOrNull() ?: 0,
            p50Time = percentile(executionTimes, 0.5),
            p95Time = percentile(executionTimes, 0.95),
            p99Time = percentile(executionTimes, 0.99),
            throughput = if (averageTime > 0) 1000.0 / averageTime else 0.0
        )
    }
    
    /**
     * 获取所有操作的性能统计
     */
    fun getAllPerformanceStats(): Map<String, PerformanceStats> {
        return operationCounters.keys.mapNotNull { operation ->
            getPerformanceStats(operation)?.let { operation to it }
        }.toMap()
    }
    
    /**
     * 获取性能报告
     */
    fun generatePerformanceReport(): String {
        val stats = getAllPerformanceStats()
        
        if (stats.isEmpty()) {
            return "暂无性能数据"
        }
        
        val report = StringBuilder()
        report.appendLine("=== DroidRun 性能报告 ===")
        report.appendLine("生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        report.appendLine()
        
        // 按平均执行时间排序
        val sortedStats = stats.values.sortedByDescending { it.averageTime }
        
        report.appendLine("操作性能统计 (按平均执行时间排序):")
        report.appendLine("%-30s %8s %10s %10s %8s %8s %8s".format(
            "操作", "次数", "总时间(ms)", "平均(ms)", "最小(ms)", "最大(ms)", "吞吐量/s"
        ))
        report.appendLine("-".repeat(90))
        
        sortedStats.forEach { stat ->
            report.appendLine("%-30s %8d %10d %10.2f %8d %8d %8.2f".format(
                stat.operation.take(30),
                stat.totalExecutions,
                stat.totalTime,
                stat.averageTime,
                stat.minTime,
                stat.maxTime,
                stat.throughput
            ))
        }
        
        report.appendLine()
        report.appendLine("性能分析:")
        
        // 找出最慢的操作
        val slowestOperation = sortedStats.firstOrNull()
        if (slowestOperation != null) {
            report.appendLine("• 最慢操作: ${slowestOperation.operation} (平均 ${slowestOperation.averageTime.toInt()}ms)")
        }
        
        // 找出最频繁的操作
        val mostFrequentOperation = sortedStats.maxByOrNull { it.totalExecutions }
        if (mostFrequentOperation != null) {
            report.appendLine("• 最频繁操作: ${mostFrequentOperation.operation} (${mostFrequentOperation.totalExecutions}次)")
        }
        
        // 计算总体性能
        val totalOperations = sortedStats.sumOf { it.totalExecutions }
        val totalTime = sortedStats.sumOf { it.totalTime }
        val overallThroughput = if (totalTime > 0) totalOperations * 1000.0 / totalTime else 0.0
        
        report.appendLine("• 总操作数: $totalOperations")
        report.appendLine("• 总执行时间: ${totalTime}ms")
        report.appendLine("• 整体吞吐量: ${"%.2f".format(overallThroughput)}操作/秒")
        
        return report.toString()
    }
    
    /**
     * 开始实时性能监控
     */
    fun startRealTimeMonitoring(intervalMs: Long = 5000) {
        if (isMonitoring) return
        
        isMonitoring = true
        monitoringJob = CoroutineScope(Dispatchers.Default).launch {
            while (isActive && isMonitoring) {
                try {
                    logCurrentPerformanceStatus()
                    delay(intervalMs)
                } catch (e: Exception) {
                    Timber.e(e, "实时性能监控异常")
                }
            }
        }
        
        Timber.i("开始实时性能监控，间隔: ${intervalMs}ms")
    }
    
    /**
     * 停止实时性能监控
     */
    fun stopRealTimeMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
        Timber.i("停止实时性能监控")
    }
    
    /**
     * 清除性能数据
     */
    fun clearPerformanceData() {
        metricsHistory.clear()
        operationCounters.clear()
        totalExecutionTimes.clear()
        Timber.i("性能数据已清除")
    }
    
    /**
     * 记录当前性能状态
     */
    private fun logCurrentPerformanceStatus() {
        val memoryUsage = getMemoryUsage()
        val cpuUsage = getCpuUsage()
        val activeOperations = operationCounters.size
        
        Timber.i("性能状态 - 内存: ${memoryUsage}KB, CPU: ${"%.1f".format(cpuUsage)}%, 活跃操作: $activeOperations")
    }
    
    /**
     * 计算百分位数
     */
    private fun percentile(sortedList: List<Long>, percentile: Double): Long {
        if (sortedList.isEmpty()) return 0
        
        val index = (percentile * (sortedList.size - 1)).toInt()
        return sortedList[index.coerceIn(0, sortedList.size - 1)]
    }
    
    /**
     * 获取当前内存使用量 (KB)
     */
    private fun getMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return (runtime.totalMemory() - runtime.freeMemory()) / 1024
    }
    
    /**
     * 获取CPU使用率 (简化实现)
     */
    private fun getCpuUsage(): Float {
        // 简化实现，实际应该读取 /proc/stat 或使用其他方法
        return 0.0f
    }
    
    /**
     * 性能基准测试
     */
    fun runBenchmark(operation: String, iterations: Int = 100, warmupIterations: Int = 10) {
        Timber.i("开始性能基准测试: $operation (预热: $warmupIterations, 测试: $iterations)")
        
        // 预热
        repeat(warmupIterations) {
            measureExecutionTime("${operation}_warmup") {
                // 空操作，用于预热JVM
                Thread.yield()
            }
        }
        
        // 正式测试
        val results = mutableListOf<Long>()
        repeat(iterations) {
            val time = measureTimeMillis {
                // 这里应该放入要测试的具体操作
                Thread.yield()
            }
            results.add(time)
        }
        
        // 分析结果
        val sortedResults = results.sorted()
        val average = results.average()
        val min = sortedResults.first()
        val max = sortedResults.last()
        val p50 = percentile(sortedResults, 0.5)
        val p95 = percentile(sortedResults, 0.95)
        
        Timber.i("基准测试结果 [$operation]: 平均=${average.toInt()}ms, 最小=${min}ms, 最大=${max}ms, P50=${p50}ms, P95=${p95}ms")
    }
}
