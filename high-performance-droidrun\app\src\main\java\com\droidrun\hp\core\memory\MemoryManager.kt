package com.droidrun.hp.core.memory

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import kotlinx.coroutines.*
import timber.log.Timber
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 智能内存管理器
 * 动态监控和优化内存使用，防止OOM
 */
@Singleton
class MemoryManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val MEMORY_CHECK_INTERVAL = 5000L // 5秒检查一次
        private const val LOW_MEMORY_THRESHOLD = 0.15f // 可用内存低于15%时触发清理
        private const val CRITICAL_MEMORY_THRESHOLD = 0.08f // 可用内存低于8%时强制清理
        private const val MAX_CACHE_SIZE_MB = 200 // 最大缓存200MB
    }
    
    // 内存监控
    private var isMonitoring = false
    private var monitoringJob: Job? = null
    private val memoryPressureCallbacks = mutableListOf<WeakReference<MemoryPressureCallback>>()
    
    // 缓存管理
    private val managedCaches = ConcurrentHashMap<String, ManagedCache>()
    private val totalCacheSize = AtomicLong(0)
    
    // 内存统计
    private var lastGCTime = 0L
    private var gcCount = 0
    
    /**
     * 内存压力回调接口
     */
    interface MemoryPressureCallback {
        fun onLowMemory()
        fun onCriticalMemory()
        fun onMemoryRecovered()
    }
    
    /**
     * 托管缓存接口
     */
    interface ManagedCache {
        fun getSize(): Long
        fun clear()
        fun trim(targetSize: Long)
        fun getPriority(): Int // 优先级，数字越小越重要
    }
    
    /**
     * 内存状态
     */
    data class MemoryStatus(
        val totalMemory: Long,
        val availableMemory: Long,
        val usedMemory: Long,
        val cacheMemory: Long,
        val memoryPressure: MemoryPressure
    ) {
        val usagePercent: Float
            get() = if (totalMemory > 0) usedMemory.toFloat() / totalMemory else 0f
        
        val availablePercent: Float
            get() = if (totalMemory > 0) availableMemory.toFloat() / totalMemory else 0f
    }
    
    /**
     * 内存压力等级
     */
    enum class MemoryPressure {
        NORMAL,    // 正常
        LOW,       // 内存不足
        CRITICAL   // 内存严重不足
    }
    
    /**
     * 启动内存监控
     */
    fun startMemoryMonitoring() {
        if (isMonitoring) return
        
        isMonitoring = true
        monitoringJob = CoroutineScope(Dispatchers.Default).launch {
            while (isActive && isMonitoring) {
                try {
                    checkMemoryStatus()
                    delay(MEMORY_CHECK_INTERVAL)
                } catch (e: Exception) {
                    Timber.e(e, "内存监控异常")
                }
            }
        }
        
        Timber.i("内存监控已启动")
    }
    
    /**
     * 停止内存监控
     */
    fun stopMemoryMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
        Timber.i("内存监控已停止")
    }
    
    /**
     * 注册内存压力回调
     */
    fun registerMemoryPressureCallback(callback: MemoryPressureCallback) {
        memoryPressureCallbacks.add(WeakReference(callback))
    }
    
    /**
     * 注册托管缓存
     */
    fun registerManagedCache(name: String, cache: ManagedCache) {
        managedCaches[name] = cache
        updateTotalCacheSize()
        Timber.d("注册托管缓存: $name")
    }
    
    /**
     * 注销托管缓存
     */
    fun unregisterManagedCache(name: String) {
        managedCaches.remove(name)
        updateTotalCacheSize()
        Timber.d("注销托管缓存: $name")
    }
    
    /**
     * 获取当前内存状态
     */
    fun getMemoryStatus(): MemoryStatus {
        val memInfo = ActivityManager.MemoryInfo()
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        activityManager.getMemoryInfo(memInfo)
        
        val totalMemory = memInfo.totalMem
        val availableMemory = memInfo.availMem
        val usedMemory = totalMemory - availableMemory
        val cacheMemory = totalCacheSize.get() * 1024 * 1024 // 转换为字节
        
        val memoryPressure = when {
            memInfo.availMem.toFloat() / memInfo.totalMem < CRITICAL_MEMORY_THRESHOLD -> MemoryPressure.CRITICAL
            memInfo.availMem.toFloat() / memInfo.totalMem < LOW_MEMORY_THRESHOLD -> MemoryPressure.LOW
            else -> MemoryPressure.NORMAL
        }
        
        return MemoryStatus(
            totalMemory = totalMemory,
            availableMemory = availableMemory,
            usedMemory = usedMemory,
            cacheMemory = cacheMemory,
            memoryPressure = memoryPressure
        )
    }
    
    /**
     * 手动触发内存清理
     */
    fun triggerMemoryCleanup(aggressive: Boolean = false) {
        Timber.i("开始内存清理 (激进模式: $aggressive)")
        
        val beforeStatus = getMemoryStatus()
        
        if (aggressive) {
            // 激进清理：清空所有缓存
            clearAllCaches()
            
            // 强制GC
            forceGarbageCollection()
            
            // 通知组件释放非必要资源
            notifyMemoryPressure(MemoryPressure.CRITICAL)
        } else {
            // 温和清理：按优先级清理缓存
            trimCachesByPriority()
            
            // 建议GC
            suggestGarbageCollection()
            
            // 通知低内存状态
            notifyMemoryPressure(MemoryPressure.LOW)
        }
        
        val afterStatus = getMemoryStatus()
        val freedMemory = afterStatus.availableMemory - beforeStatus.availableMemory
        
        Timber.i("内存清理完成，释放内存: ${freedMemory / 1024 / 1024}MB")
    }
    
    /**
     * 检查内存状态
     */
    private fun checkMemoryStatus() {
        val status = getMemoryStatus()
        
        when (status.memoryPressure) {
            MemoryPressure.CRITICAL -> {
                Timber.w("内存严重不足: ${status.availablePercent * 100}%")
                triggerMemoryCleanup(aggressive = true)
            }
            MemoryPressure.LOW -> {
                Timber.w("内存不足: ${status.availablePercent * 100}%")
                triggerMemoryCleanup(aggressive = false)
            }
            MemoryPressure.NORMAL -> {
                // 正常状态，检查缓存大小
                if (status.cacheMemory > MAX_CACHE_SIZE_MB * 1024 * 1024) {
                    Timber.d("缓存过大，进行清理")
                    trimCachesByPriority()
                }
            }
        }
    }
    
    /**
     * 清空所有缓存
     */
    private fun clearAllCaches() {
        managedCaches.values.forEach { cache ->
            try {
                cache.clear()
            } catch (e: Exception) {
                Timber.e(e, "清理缓存失败")
            }
        }
        updateTotalCacheSize()
    }
    
    /**
     * 按优先级清理缓存
     */
    private fun trimCachesByPriority() {
        val sortedCaches = managedCaches.values.sortedByDescending { it.getPriority() }
        var targetReduction = (totalCacheSize.get() * 0.3).toLong() // 减少30%
        
        for (cache in sortedCaches) {
            if (targetReduction <= 0) break
            
            val currentSize = cache.getSize()
            val trimSize = minOf(targetReduction, currentSize / 2) // 最多减少一半
            
            try {
                cache.trim(currentSize - trimSize)
                targetReduction -= trimSize
            } catch (e: Exception) {
                Timber.e(e, "清理缓存失败")
            }
        }
        
        updateTotalCacheSize()
    }
    
    /**
     * 强制垃圾回收
     */
    private fun forceGarbageCollection() {
        val beforeMemory = Debug.getNativeHeapAllocatedSize()
        
        System.gc()
        System.runFinalization()
        System.gc()
        
        val afterMemory = Debug.getNativeHeapAllocatedSize()
        val freedMemory = beforeMemory - afterMemory
        
        gcCount++
        lastGCTime = System.currentTimeMillis()
        
        Timber.d("强制GC完成，释放: ${freedMemory / 1024}KB")
    }
    
    /**
     * 建议垃圾回收
     */
    private fun suggestGarbageCollection() {
        // 避免频繁GC
        if (System.currentTimeMillis() - lastGCTime > 10000) {
            System.gc()
            lastGCTime = System.currentTimeMillis()
            gcCount++
        }
    }
    
    /**
     * 通知内存压力
     */
    private fun notifyMemoryPressure(pressure: MemoryPressure) {
        // 清理失效的弱引用
        memoryPressureCallbacks.removeAll { it.get() == null }
        
        memoryPressureCallbacks.forEach { ref ->
            ref.get()?.let { callback ->
                try {
                    when (pressure) {
                        MemoryPressure.LOW -> callback.onLowMemory()
                        MemoryPressure.CRITICAL -> callback.onCriticalMemory()
                        MemoryPressure.NORMAL -> callback.onMemoryRecovered()
                    }
                } catch (e: Exception) {
                    Timber.e(e, "内存压力回调异常")
                }
            }
        }
    }
    
    /**
     * 更新总缓存大小
     */
    private fun updateTotalCacheSize() {
        val total = managedCaches.values.sumOf { it.getSize() }
        totalCacheSize.set(total)
    }
    
    /**
     * 获取内存使用报告
     */
    fun getMemoryReport(): String {
        val status = getMemoryStatus()
        val report = StringBuilder()
        
        report.appendLine("=== 内存使用报告 ===")
        report.appendLine("总内存: ${status.totalMemory / 1024 / 1024}MB")
        report.appendLine("可用内存: ${status.availableMemory / 1024 / 1024}MB (${(status.availablePercent * 100).toInt()}%)")
        report.appendLine("已用内存: ${status.usedMemory / 1024 / 1024}MB (${(status.usagePercent * 100).toInt()}%)")
        report.appendLine("缓存内存: ${status.cacheMemory / 1024 / 1024}MB")
        report.appendLine("内存压力: ${status.memoryPressure}")
        report.appendLine("GC次数: $gcCount")
        report.appendLine()
        
        report.appendLine("托管缓存:")
        managedCaches.forEach { (name, cache) ->
            report.appendLine("  $name: ${cache.getSize()}MB (优先级: ${cache.getPriority()})")
        }
        
        return report.toString()
    }
}
