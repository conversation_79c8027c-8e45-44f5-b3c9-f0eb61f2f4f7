#ifndef DROIDRUN_INFERENCE_ENGINE_H
#define DROIDRUN_INFERENCE_ENGINE_H

#include <string>
#include <memory>
#include <functional>
#include <mutex>
#include <vector>

// 条件编译支持不同的推理后端
#ifdef USE_LLAMA_CPP
#include "llama.h"
#endif

#ifdef USE_ONNX_RUNTIME
#include "onnxruntime_cxx_api.h"
#endif

namespace droidrun {
namespace llm {

/**
 * 推理后端类型
 */
enum class BackendType {
    LLAMA_CPP,
    ONNX_RUNTIME,
    TENSORFLOW_LITE
};

/**
 * 性能统计信息
 */
struct PerformanceStats {
    long total_inferences = 0;
    long total_time_ms = 0;
    long total_tokens = 0;
    long min_time_ms = 0;
    long max_time_ms = 0;
    double avg_time_ms = 0.0;
    double tokens_per_second = 0.0;
};

/**
 * 高性能推理引擎
 * 支持多种推理后端：llama.cpp, ONNX Runtime, TensorFlow Lite
 */
class InferenceEngine {
public:
    InferenceEngine();
    ~InferenceEngine();
    
    /**
     * 初始化推理引擎
     * @param model_path 模型文件路径
     * @param context_length 上下文长度
     * @param threads 线程数
     * @return 是否初始化成功
     */
    bool Initialize(const std::string& model_path, int context_length, int threads);
    
    /**
     * 执行推理
     * @param prompt 输入提示
     * @param max_tokens 最大生成token数
     * @return 生成的文本
     */
    std::string Inference(const std::string& prompt, int max_tokens = 512);
    
    /**
     * 流式推理
     * @param prompt 输入提示
     * @param max_tokens 最大生成token数
     * @param token_callback token回调函数
     * @return 完整生成的文本
     */
    std::string StreamInference(const std::string& prompt, 
                               int max_tokens,
                               std::function<void(const std::string&)> token_callback);
    
    /**
     * 释放资源
     */
    void Release();
    
    /**
     * 获取性能统计信息
     */
    PerformanceStats GetPerformanceStats() const;
    
    /**
     * 检查是否已初始化
     */
    bool IsInitialized() const { return initialized_; }
    
    /**
     * 获取后端类型
     */
    BackendType GetBackendType() const { return backend_type_; }

private:
    // 初始化不同的推理后端
    bool InitializeLlamaCpp(const std::string& model_path);
    bool InitializeOnnxRuntime(const std::string& model_path);
    bool InitializeTensorFlowLite(const std::string& model_path);
    
    // 不同后端的推理实现
    std::string InferenceWithLlamaCpp(const std::string& prompt, int max_tokens);
    std::string InferenceWithOnnxRuntime(const std::string& prompt, int max_tokens);
    std::string InferenceWithTensorFlowLite(const std::string& prompt, int max_tokens);
    
    // 流式推理实现
    std::string StreamInferenceWithLlamaCpp(const std::string& prompt, 
                                           int max_tokens,
                                           std::function<void(const std::string&)> token_callback);
    std::string StreamInferenceWithOnnxRuntime(const std::string& prompt, 
                                              int max_tokens,
                                              std::function<void(const std::string&)> token_callback);
    
    // 释放不同后端的资源
    void ReleaseLlamaCpp();
    void ReleaseOnnxRuntime();
    void ReleaseTensorFlowLite();
    
    // 辅助方法
    void WarmupModel();
    void UpdatePerformanceStats(long duration_ms, size_t output_length);
    bool IsGPUAvailable();
    
#ifdef USE_LLAMA_CPP
    // llama.cpp 相关方法
    llama_token SampleNextToken(llama_context* ctx);
#endif
    
#ifdef USE_ONNX_RUNTIME
    // ONNX Runtime 相关方法
    std::vector<Ort::Value> PreprocessInput(const std::string& prompt);
    std::string PostprocessOutput(const std::vector<Ort::Value>& outputs, int max_tokens);
    void GetModelInputOutputInfo();
#endif

private:
    // 基本状态
    bool initialized_;
    BackendType backend_type_;
    void* model_handle_;
    
    // 模型参数
    int context_length_;
    int max_tokens_;
    float temperature_;
    int threads_;
    
    // 性能统计
    mutable std::mutex stats_mutex_;
    PerformanceStats performance_stats_;
    
#ifdef USE_LLAMA_CPP
    // llama.cpp 特定成员
    llama_model* llama_model_ = nullptr;
#endif
    
#ifdef USE_ONNX_RUNTIME
    // ONNX Runtime 特定成员
    std::unique_ptr<Ort::Env> onnx_env_;
    std::unique_ptr<Ort::Session> onnx_session_;
    std::vector<const char*> input_names_;
    std::vector<const char*> output_names_;
    std::vector<std::vector<int64_t>> input_shapes_;
    std::vector<std::vector<int64_t>> output_shapes_;
#endif
};

} // namespace llm
} // namespace droidrun

#endif // DROIDRUN_INFERENCE_ENGINE_H
