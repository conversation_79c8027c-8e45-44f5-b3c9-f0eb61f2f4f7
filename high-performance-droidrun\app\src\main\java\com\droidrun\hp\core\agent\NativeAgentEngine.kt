package com.droidrun.hp.core.agent

import com.droidrun.hp.core.engine.LocalLLMEngine
import com.droidrun.hp.core.engine.LocalRAGEngine
import com.droidrun.hp.data.model.*
import com.droidrun.hp.service.HighPerformanceAccessibilityService
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 原生Agent执行引擎
 * 整合LLM、RAG和UI操作，提供高性能的任务执行
 */
@Singleton
class NativeAgentEngine @Inject constructor(
    private val llmEngine: LocalLLMEngine,
    private val ragEngine: LocalRAGEngine,
    private val operationOptimizer: OperationOptimizer,
    private val uiPredictor: UIPredictor
) {
    
    companion object {
        private const val MAX_STEPS = 20
        private const val MAX_RETRIES = 3
        private const val STEP_TIMEOUT_MS = 30000L
        private const val UI_WAIT_TIMEOUT_MS = 5000L
    }
    
    // 引擎状态
    private val isInitialized = AtomicBoolean(false)
    private val runningTasks = mutableMapOf<String, Job>()
    
    // 性能统计
    private val taskCounter = AtomicLong(0)
    private val successCounter = AtomicLong(0)
    private val totalExecutionTime = AtomicLong(0)
    
    // 执行上下文
    private val activeContexts = mutableMapOf<String, AgentContext>()
    
    /**
     * 初始化Agent引擎
     */
    suspend fun initialize() {
        if (isInitialized.get()) {
            Timber.w("Agent引擎已经初始化")
            return
        }
        
        try {
            Timber.i("开始初始化Agent引擎...")
            
            PerformanceProfiler.measureExecutionTime("AgentEngine.Initialize") {
                // 确保依赖的引擎已初始化
                if (!llmEngine.getPerformanceStats().isInitialized) {
                    throw IllegalStateException("LLM引擎未初始化")
                }
                
                if (!ragEngine.getPerformanceStats().isInitialized) {
                    throw IllegalStateException("RAG引擎未初始化")
                }
                
                // 初始化操作优化器
                operationOptimizer.initialize()
                
                // 初始化UI预测器
                uiPredictor.initialize()
                
                isInitialized.set(true)
                Timber.i("Agent引擎初始化完成")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Agent引擎初始化失败")
            throw e
        }
    }
    
    /**
     * 执行任务
     */
    suspend fun executeTask(taskId: String, command: String): TaskResult {
        if (!isInitialized.get()) {
            throw IllegalStateException("Agent引擎未初始化")
        }
        
        // 取消同ID的现有任务
        runningTasks[taskId]?.cancel()
        
        val startTime = System.currentTimeMillis()
        taskCounter.incrementAndGet()
        
        return try {
            val result = withContext(Dispatchers.Default) {
                val job = async {
                    executeTaskInternal(taskId, command, startTime)
                }
                runningTasks[taskId] = job
                job.await()
            }
            
            if (result.success) {
                successCounter.incrementAndGet()
            }
            
            val executionTime = System.currentTimeMillis() - startTime
            totalExecutionTime.addAndGet(executionTime)
            
            result
            
        } catch (e: CancellationException) {
            Timber.i("任务 [$taskId] 被取消")
            TaskResult(
                taskId = taskId,
                command = command,
                success = false,
                message = "任务被取消",
                startTime = startTime,
                endTime = System.currentTimeMillis(),
                executedActions = emptyList(),
                errorDetails = "Task cancelled"
            )
        } catch (e: Exception) {
            Timber.e(e, "任务 [$taskId] 执行异常")
            TaskResult(
                taskId = taskId,
                command = command,
                success = false,
                message = "执行异常: ${e.message}",
                startTime = startTime,
                endTime = System.currentTimeMillis(),
                executedActions = emptyList(),
                errorDetails = e.stackTraceToString()
            )
        } finally {
            runningTasks.remove(taskId)
            activeContexts.remove(taskId)
        }
    }
    
    /**
     * 取消任务
     */
    fun cancelTask(taskId: String): Boolean {
        return runningTasks[taskId]?.let { job ->
            job.cancel()
            runningTasks.remove(taskId)
            activeContexts.remove(taskId)
            true
        } ?: false
    }
    
    /**
     * 获取任务状态
     */
    fun getTaskStatus(taskId: String): TaskStatus {
        return when {
            runningTasks.containsKey(taskId) -> TaskStatus.RUNNING
            activeContexts.containsKey(taskId) -> TaskStatus.RUNNING
            else -> TaskStatus.COMPLETED
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): AgentPerformanceStats {
        return AgentPerformanceStats(
            isInitialized = isInitialized.get(),
            runningTaskCount = runningTasks.size,
            totalTaskCount = taskCounter.get(),
            successTaskCount = successCounter.get(),
            totalExecutionTime = totalExecutionTime.get(),
            averageExecutionTime = if (taskCounter.get() > 0) {
                totalExecutionTime.get() / taskCounter.get()
            } else 0L,
            successRate = if (taskCounter.get() > 0) {
                successCounter.get().toFloat() / taskCounter.get()
            } else 0f
        )
    }
    
    /**
     * 内部任务执行逻辑
     */
    private suspend fun executeTaskInternal(
        taskId: String, 
        command: String, 
        startTime: Long
    ): TaskResult {
        val executedActions = mutableListOf<UIAction>()
        val steps = mutableListOf<AgentStep>()
        
        // 创建执行上下文
        val context = AgentContext(
            taskId = taskId,
            goal = command,
            currentStep = 0,
            maxSteps = MAX_STEPS,
            history = emptyList(),
            availableTools = getAvailableTools()
        )
        activeContexts[taskId] = context
        
        try {
            Timber.i("开始执行任务 [$taskId]: $command")
            
            // 获取初始UI状态
            var currentUIState = getCurrentUIState()
            context.copy(currentUIState = currentUIState)
            
            // 执行ReAct循环
            for (stepNumber in 1..MAX_STEPS) {
                if (!isActive) break
                
                val stepStartTime = System.currentTimeMillis()
                
                try {
                    // 1. 思考阶段 - 使用RAG增强的推理
                    val thinkingStep = performThinking(context, currentUIState)
                    steps.add(thinkingStep)
                    
                    if (thinkingStep.action == "complete") {
                        Timber.i("任务 [$taskId] 在第 $stepNumber 步完成")
                        break
                    }
                    
                    // 2. 行动阶段 - 执行优化后的操作
                    val actionStep = performAction(context, thinkingStep, currentUIState)
                    steps.add(actionStep)
                    
                    if (actionStep.success && actionStep.action != "no_action") {
                        // 解析并记录执行的操作
                        parseAndRecordAction(actionStep, executedActions)
                    }
                    
                    // 3. 观察阶段 - 获取新的UI状态
                    currentUIState = waitForUIUpdate(currentUIState)
                    val observationStep = performObservation(currentUIState, actionStep.success)
                    steps.add(observationStep)
                    
                    // 4. 更新上下文
                    val updatedContext = context.copy(
                        currentStep = stepNumber,
                        history = steps,
                        currentUIState = currentUIState
                    )
                    activeContexts[taskId] = updatedContext
                    
                    val stepTime = System.currentTimeMillis() - stepStartTime
                    Timber.d("步骤 $stepNumber 完成，耗时: ${stepTime}ms")
                    
                    // 检查是否达到目标
                    if (actionStep.action == "complete" || isGoalAchieved(context, steps)) {
                        Timber.i("任务 [$taskId] 目标达成")
                        break
                    }
                    
                } catch (e: Exception) {
                    Timber.e(e, "步骤 $stepNumber 执行失败")
                    
                    val errorStep = AgentStep(
                        stepNumber = stepNumber,
                        type = AgentStep.StepType.ERROR,
                        thought = "步骤执行失败: ${e.message}",
                        action = "error",
                        parameters = emptyMap(),
                        result = e.message ?: "未知错误",
                        success = false,
                        executionTimeMs = System.currentTimeMillis() - stepStartTime
                    )
                    steps.add(errorStep)
                    
                    // 如果是关键错误，终止执行
                    if (e is CancellationException) {
                        throw e
                    }
                }
            }
            
            val finalUIState = getCurrentUIState()
            val success = steps.any { it.action == "complete" && it.success }
            
            return TaskResult(
                taskId = taskId,
                command = command,
                success = success,
                message = if (success) "任务执行成功" else "任务执行完成但可能未达到预期目标",
                startTime = startTime,
                endTime = System.currentTimeMillis(),
                executedActions = executedActions,
                finalUIState = finalUIState
            )
            
        } catch (e: Exception) {
            throw e
        }
    }
    
    /**
     * 执行思考阶段
     */
    private suspend fun performThinking(
        context: AgentContext, 
        currentUIState: UIState?
    ): AgentStep {
        val stepStartTime = System.currentTimeMillis()
        
        return try {
            // 构建RAG查询
            val ragQuery = RAGQuery(
                text = buildRAGQueryText(context, currentUIState),
                category = "android_automation",
                maxResults = 5,
                similarityThreshold = 0.7f
            )
            
            // 查询相关知识
            val ragResult = ragEngine.query(ragQuery)
            
            // 构建LLM提示
            val prompt = buildLLMPrompt(context, currentUIState, ragResult)
            
            // LLM推理
            val response = llmEngine.inference(prompt, 256)
            
            // 解析响应
            val parsedResponse = parseAgentResponse(response)
            
            AgentStep(
                stepNumber = context.currentStep + 1,
                type = AgentStep.StepType.THINKING,
                thought = parsedResponse.thought,
                action = parsedResponse.action,
                parameters = parsedResponse.parameters,
                result = "思考完成",
                success = true,
                executionTimeMs = System.currentTimeMillis() - stepStartTime
            )
            
        } catch (e: Exception) {
            Timber.e(e, "思考阶段失败")
            AgentStep(
                stepNumber = context.currentStep + 1,
                type = AgentStep.StepType.ERROR,
                thought = "思考阶段失败: ${e.message}",
                action = "error",
                parameters = emptyMap(),
                result = e.message ?: "未知错误",
                success = false,
                executionTimeMs = System.currentTimeMillis() - stepStartTime
            )
        }
    }
    
    /**
     * 执行行动阶段
     */
    private suspend fun performAction(
        context: AgentContext,
        thinkingStep: AgentStep,
        currentUIState: UIState?
    ): AgentStep {
        val stepStartTime = System.currentTimeMillis()
        
        return try {
            val action = createUIAction(thinkingStep, currentUIState)
            
            if (action != null) {
                // 使用操作优化器优化操作
                val optimizedActions = operationOptimizer.optimizeActionSequence(listOf(action))
                
                // 执行优化后的操作
                val success = executeUIActions(optimizedActions)
                
                AgentStep(
                    stepNumber = context.currentStep + 1,
                    type = AgentStep.StepType.ACTION,
                    thought = thinkingStep.thought,
                    action = thinkingStep.action,
                    parameters = thinkingStep.parameters,
                    result = if (success) "操作执行成功" else "操作执行失败",
                    success = success,
                    executionTimeMs = System.currentTimeMillis() - stepStartTime
                )
            } else {
                AgentStep(
                    stepNumber = context.currentStep + 1,
                    type = AgentStep.StepType.ACTION,
                    thought = thinkingStep.thought,
                    action = "no_action",
                    parameters = emptyMap(),
                    result = "无需执行操作",
                    success = true,
                    executionTimeMs = System.currentTimeMillis() - stepStartTime
                )
            }
            
        } catch (e: Exception) {
            Timber.e(e, "行动阶段失败")
            AgentStep(
                stepNumber = context.currentStep + 1,
                type = AgentStep.StepType.ERROR,
                thought = thinkingStep.thought,
                action = "error",
                parameters = emptyMap(),
                result = e.message ?: "未知错误",
                success = false,
                executionTimeMs = System.currentTimeMillis() - stepStartTime
            )
        }
    }
    
    /**
     * 执行观察阶段
     */
    private suspend fun performObservation(
        currentUIState: UIState?,
        actionSuccess: Boolean
    ): AgentStep {
        val stepStartTime = System.currentTimeMillis()
        
        val observation = if (currentUIState != null) {
            "当前界面包含 ${currentUIState.elements.size} 个可交互元素。" +
            if (actionSuccess) "上一个操作执行成功。" else "上一个操作可能失败。"
        } else {
            "无法获取当前UI状态。"
        }
        
        return AgentStep(
            stepNumber = 0, // 观察步骤不计入步骤数
            type = AgentStep.StepType.OBSERVATION,
            thought = "",
            action = "observe",
            parameters = emptyMap(),
            result = observation,
            success = true,
            executionTimeMs = System.currentTimeMillis() - stepStartTime
        )
    }
    
    /**
     * 获取当前UI状态
     */
    private suspend fun getCurrentUIState(): UIState? {
        return withContext(Dispatchers.Main) {
            HighPerformanceAccessibilityService.getInstance()?.getCurrentUIState()
        }
    }
    
    /**
     * 等待UI更新
     */
    private suspend fun waitForUIUpdate(previousState: UIState?): UIState? {
        val startTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - startTime < UI_WAIT_TIMEOUT_MS) {
            val currentState = getCurrentUIState()
            
            if (currentState != null && currentState != previousState) {
                return currentState
            }
            
            delay(100) // 等待100ms后重试
        }
        
        return getCurrentUIState() // 超时后返回当前状态
    }
    
    /**
     * 执行UI操作
     */
    private suspend fun executeUIActions(actions: List<UIAction>): Boolean {
        return withContext(Dispatchers.Main) {
            val accessibilityService = HighPerformanceAccessibilityService.getInstance()
                ?: return@withContext false
            
            actions.all { action ->
                accessibilityService.executeActionDirect(action)
            }
        }
    }
    
    /**
     * 获取可用工具列表
     */
    private fun getAvailableTools(): List<String> {
        return listOf(
            "tap", "long_tap", "swipe", "scroll", "input_text", 
            "press_key", "wait", "complete", "extract_text"
        )
    }
    
    // 其他辅助方法的实现...
    private fun buildRAGQueryText(context: AgentContext, currentUIState: UIState?): String {
        return "目标: ${context.goal}\n当前步骤: ${context.currentStep}\n" +
               "UI元素数量: ${currentUIState?.elements?.size ?: 0}"
    }
    
    private fun buildLLMPrompt(context: AgentContext, currentUIState: UIState?, ragResult: RAGResult): String {
        return "基于以下信息执行Android自动化任务:\n" +
               "目标: ${context.goal}\n" +
               "相关知识: ${ragResult.context}\n" +
               "当前UI状态: ${currentUIState?.elements?.take(10)}\n" +
               "请返回JSON格式的响应，包含thought、action和parameters字段。"
    }
    
    private fun parseAgentResponse(response: String): ParsedResponse {
        return try {
            Json.decodeFromString<ParsedResponse>(response)
        } catch (e: Exception) {
            ParsedResponse("解析响应失败", "error", emptyMap())
        }
    }
    
    private fun createUIAction(step: AgentStep, currentUIState: UIState?): UIAction? {
        // 根据步骤信息创建UI操作
        return when (step.action) {
            "tap" -> {
                val elementId = step.parameters["element_id"] ?: return null
                UIAction(
                    id = java.util.UUID.randomUUID().toString(),
                    type = UIAction.Type.CLICK,
                    targetElementId = elementId
                )
            }
            // 其他操作类型...
            else -> null
        }
    }
    
    private fun parseAndRecordAction(step: AgentStep, executedActions: MutableList<UIAction>) {
        // 解析步骤并记录执行的操作
    }
    
    private fun isGoalAchieved(context: AgentContext, steps: List<AgentStep>): Boolean {
        // 检查是否达到目标
        return steps.any { it.action == "complete" && it.success }
    }
}

/**
 * 解析后的Agent响应
 */
private data class ParsedResponse(
    val thought: String,
    val action: String,
    val parameters: Map<String, String>
)

/**
 * Agent性能统计
 */
data class AgentPerformanceStats(
    val isInitialized: Boolean,
    val runningTaskCount: Int,
    val totalTaskCount: Long,
    val successTaskCount: Long,
    val totalExecutionTime: Long,
    val averageExecutionTime: Long,
    val successRate: Float
)
