@echo off
echo Fixing Java Version for DroidRun Build
echo =======================================

echo Current Java version:
java -version
echo.

echo Checking Java version compatibility...
java -version 2>&1 | find "1.8" >nul
if %errorlevel% equ 0 (
    echo WARNING: Java 8 detected. Android development requires Java 11+
    echo.
    goto :download_java
)

java -version 2>&1 | find "11" >nul
if %errorlevel% equ 0 (
    echo OK: Java 11 detected
    goto :build
)

java -version 2>&1 | find "17" >nul
if %errorlevel% equ 0 (
    echo OK: Java 17 detected
    goto :build
)

java -version 2>&1 | find "21" >nul
if %errorlevel% equ 0 (
    echo OK: Java 21 detected
    goto :build
)

:download_java
echo.
echo Java 11+ is required for Android development.
echo.
echo Option 1: Download and install Java 17 (Recommended)
echo   URL: https://adoptium.net/temurin/releases/?version=17
echo.
echo Option 2: Use portable Java (automatic download)
echo.
set /p choice="Choose option (1 for manual download, 2 for automatic): "

if "%choice%"=="1" (
    echo.
    echo Please download and install Java 17 from:
    echo https://adoptium.net/temurin/releases/?version=17
    echo.
    echo After installation, restart this script.
    pause
    exit /b 1
)

if "%choice%"=="2" (
    goto :download_portable_java
)

echo Invalid choice. Please run the script again.
pause
exit /b 1

:download_portable_java
echo.
echo Downloading portable Java 17...
echo This may take several minutes...

if not exist "C:\java" mkdir C:\java

echo Downloading OpenJDK 17...
powershell -Command "& {try { Write-Host 'Downloading Java 17...'; Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip' -OutFile 'C:\java\openjdk17.zip' -UseBasicParsing; Write-Host 'Download completed' } catch { Write-Host 'Download failed: ' + $_.Exception.Message; exit 1 }}"

if not exist "C:\java\openjdk17.zip" (
    echo ERROR: Failed to download Java 17
    echo Please download manually from: https://adoptium.net/
    pause
    exit /b 1
)

echo Extracting Java 17...
powershell -Command "& {try { Expand-Archive -Path 'C:\java\openjdk17.zip' -DestinationPath 'C:\java' -Force; Write-Host 'Extraction completed' } catch { Write-Host 'Extraction failed: ' + $_.Exception.Message; exit 1 }}"

echo Setting up Java 17...
set JAVA_HOME=C:\java\jdk-17.0.9+9
set PATH=%JAVA_HOME%\bin;%PATH%

echo Verifying Java 17 installation...
%JAVA_HOME%\bin\java -version
if %errorlevel% neq 0 (
    echo ERROR: Java 17 setup failed
    pause
    exit /b 1
)

echo.
echo SUCCESS: Java 17 installed successfully!
echo.

:build
echo Setting up build environment...
echo ==============================

REM Set Java home for this session
if exist "C:\java\jdk-17.0.9+9" (
    set JAVA_HOME=C:\java\jdk-17.0.9+9
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo Using Java 17: %JAVA_HOME%
)

REM Set Gradle home
set GRADLE_HOME=C:\gradle\gradle-8.4
set PATH=%GRADLE_HOME%\bin;%PATH%

REM Set Android SDK if available
if not defined ANDROID_HOME (
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
        echo Set ANDROID_HOME to: %ANDROID_HOME%
    ) else (
        echo WARNING: Android SDK not found
        echo Please install Android Studio for best results
    )
)

echo.
echo Environment setup complete:
echo   JAVA_HOME: %JAVA_HOME%
echo   GRADLE_HOME: %GRADLE_HOME%
echo   ANDROID_HOME: %ANDROID_HOME%
echo.

echo Starting DroidRun build...
echo ==========================

echo Step 1: Cleaning project...
gradle clean

echo.
echo Step 2: Building release APK...
gradle assembleRelease --stacktrace

if %errorlevel% equ 0 (
    echo.
    echo ==========================================
    echo BUILD SUCCESS!
    echo ==========================================
    echo.
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK created: app\build\outputs\apk\release\app-release.apk
        
        for %%A in ("app\build\outputs\apk\release\app-release.apk") do (
            echo APK size: %%~zA bytes
        )
        
        echo.
        echo Installation command:
        echo   adb install app\build\outputs\apk\release\app-release.apk
        echo.
        echo Next steps:
        echo   1. Connect Android device via USB
        echo   2. Enable USB debugging
        echo   3. Run installation command
        echo   4. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)
    ) else (
        echo APK not found. Searching...
        dir /s *.apk | find ".apk"
    )
) else (
    echo.
    echo ==========================================
    echo BUILD FAILED
    echo ==========================================
    echo.
    echo Possible issues:
    echo   1. Missing Android SDK - install Android Studio
    echo   2. Network issues - check internet connection
    echo   3. Disk space - ensure sufficient free space
    echo.
    echo For detailed help, see FINAL_BUILD_SOLUTION.md
)

echo.
pause
