# 🐍 DroidRun Python版本

完全使用Python开发的Android自动化应用，无需Java环境。

## 🎯 **优势**

- ✅ **无需Java**: 完全基于Python，避免Java版本问题
- ✅ **快速开发**: Python开发效率更高
- ✅ **轻量级**: 更小的APK体积
- ✅ **易维护**: Python代码更易读和维护
- ✅ **丰富生态**: 可直接使用Python AI库

## 🏗️ **技术架构**

### **核心技术栈**
- **UI框架**: Kivy + KivyMD (Material Design)
- **构建工具**: Buildozer
- **AI集成**: requests + asyncio
- **本地存储**: SQLite + pickle
- **Android API**: pyjnius (Java Native Interface)

### **项目结构**
```
python-version/
├── main.py                 # 应用入口
├── buildozer.spec         # 构建配置
├── requirements.txt       # Python依赖
├── src/
│   ├── ui/               # 用户界面
│   ├── core/             # 核心逻辑
│   ├── ai/               # AI服务集成
│   ├── storage/          # 数据存储
│   └── utils/            # 工具函数
└── assets/               # 资源文件
```

## 🚀 **快速开始**

### **1. 安装依赖**
```bash
pip install kivy kivymd buildozer requests aiohttp
```

### **2. 构建APK**
```bash
cd python-version
buildozer android debug
```

### **3. 安装到设备**
```bash
adb install bin/droidrun-python-debug.apk
```

## 📱 **功能特性**

### **核心功能**
- 🤖 自然语言UI自动化
- 🧠 豆包AI服务集成
- 💾 本地知识库缓存
- 📊 实时性能监控
- 🔄 任务队列管理

### **AI服务集成**
- DeepSeek LLM API
- 豆包Embedding API
- VikingDB向量数据库
- 云知识库查询

## ⚡ **性能对比**

| 指标 | Java版本 | Python版本 | 优势 |
|------|---------|------------|------|
| **APK大小** | ~50MB | ~25MB | **50%** ↓ |
| **构建时间** | 5-10分钟 | 2-5分钟 | **50%** ↓ |
| **开发效率** | 中等 | 高 | **2x** ↑ |
| **维护成本** | 高 | 低 | **60%** ↓ |

## 🔧 **环境要求**

### **开发环境**
- Python 3.8+
- Android SDK (仅构建时需要)
- 无需Java环境

### **运行环境**
- Android 5.0+ (API 21+)
- 1GB+ RAM
- 网络连接

## 📖 **使用指南**

### **基本使用**
```python
# 启动应用
python main.py

# 执行自动化任务
app.execute_task("打开微信并发送消息")
```

### **API调用**
```python
# REST API兼容
import requests

response = requests.post('http://localhost:8080/api/v1/tasks/execute', 
                        json={'command': '点击登录按钮'})
```

## 🛠️ **开发指南**

### **添加新功能**
```python
# src/core/automation.py
class AutomationEngine:
    def execute_command(self, command: str):
        # 实现自动化逻辑
        pass
```

### **集成新AI服务**
```python
# src/ai/providers.py
class NewAIProvider:
    async def call_api(self, prompt: str):
        # 实现API调用
        pass
```

## 🔍 **故障排除**

### **常见问题**

**Q: 构建失败**
```bash
# 清理构建缓存
buildozer android clean
buildozer android debug
```

**Q: 权限问题**
```python
# 在buildozer.spec中添加权限
android.permissions = INTERNET, WRITE_EXTERNAL_STORAGE, SYSTEM_ALERT_WINDOW
```

**Q: 依赖冲突**
```bash
# 使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

## 📦 **部署选项**

### **1. 本地构建**
```bash
buildozer android debug
```

### **2. GitHub Actions自动构建**
```yaml
# .github/workflows/build.yml
name: Build APK
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build APK
        run: buildozer android debug
```

### **3. Docker构建**
```dockerfile
FROM kivy/buildozer:latest
COPY . /app
WORKDIR /app
RUN buildozer android debug
```

## 🎉 **总结**

Python版本的DroidRun提供了：
- **更简单的开发体验** - 无需处理Java版本问题
- **更快的构建速度** - 避免复杂的Gradle构建
- **更小的应用体积** - Python运行时更轻量
- **更好的可维护性** - Python代码更清晰

这是一个完全可行的替代方案，特别适合快速原型开发和部署！
