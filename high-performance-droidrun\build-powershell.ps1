# DroidRun PowerShell Build Script
param([string]$Task = "assembleRelease")

Write-Host "DroidRun PowerShell Build Solution" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

Write-Host "Step 1: Setting up environment..." -ForegroundColor Yellow

# Set Gradle path
$env:GRADLE_HOME = "C:\gradle\gradle-8.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"

# Check if Gradle exists
$gradlePath = "$env:GRADLE_HOME\bin\gradle.bat"
if (-not (Test-Path $gradlePath)) {
    Write-Host "ERROR: Gradle 8.4 not found at $env:GRADLE_HOME" -ForegroundColor Red
    Write-Host "Please run: .\install-gradle.bat first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "OK: Gradle 8.4 found" -ForegroundColor Green

# Set Java (try multiple locations)
$javaLocations = @(
    "C:\java\jdk-17.0.9+9",
    "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
    "C:\Program Files\Java\jdk-17*"
)

$javaFound = $false
foreach ($location in $javaLocations) {
    if (Test-Path $location) {
        $env:JAVA_HOME = $location
        $env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
        Write-Host "Using Java: $location" -ForegroundColor Green
        $javaFound = $true
        break
    }
}

if (-not $javaFound) {
    Write-Host "WARNING: JDK 17 not found, using system Java" -ForegroundColor Yellow
    try {
        java -version
    } catch {
        Write-Host "ERROR: No Java found" -ForegroundColor Red
        exit 1
    }
}

# Set Android SDK
if (-not $env:ANDROID_HOME) {
    $androidSdkPath = "$env:USERPROFILE\AppData\Local\Android\Sdk"
    if (Test-Path $androidSdkPath) {
        $env:ANDROID_HOME = $androidSdkPath
        Write-Host "Found Android SDK: $env:ANDROID_HOME" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Android SDK not found" -ForegroundColor Yellow
        Write-Host "Please install Android Studio or set ANDROID_HOME manually" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Step 2: Environment summary..." -ForegroundColor Yellow
Write-Host "GRADLE_HOME: $env:GRADLE_HOME"
Write-Host "JAVA_HOME: $env:JAVA_HOME"
Write-Host "ANDROID_HOME: $env:ANDROID_HOME"

Write-Host ""
Write-Host "Step 3: Verifying tools..." -ForegroundColor Yellow

Write-Host "Java version:"
if ($env:JAVA_HOME) {
    & "$env:JAVA_HOME\bin\java.exe" -version
} else {
    java -version
}

Write-Host ""
Write-Host "Gradle version:"
& $gradlePath --version | Select-String "Gradle"

Write-Host ""
Write-Host "Step 4: Creating gradle.properties..." -ForegroundColor Yellow

$gradleProperties = @"
# Gradle configuration for DroidRun
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
"@

if ($env:JAVA_HOME) {
    $gradleProperties += "`norg.gradle.java.home=$env:JAVA_HOME"
}

$gradleProperties | Out-File -FilePath "gradle.properties" -Encoding UTF8
Write-Host "gradle.properties created" -ForegroundColor Green

Write-Host ""
Write-Host "Step 5: Starting build process..." -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

Write-Host "Cleaning project..." -ForegroundColor Cyan
try {
    & $gradlePath clean --no-daemon
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Clean failed, but continuing..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Clean failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Building $Task..." -ForegroundColor Cyan
Write-Host "This may take several minutes for first build..." -ForegroundColor Yellow

try {
    & $gradlePath $Task --no-daemon --stacktrace --info
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "==========================================" -ForegroundColor Green
        Write-Host "BUILD SUCCESS!" -ForegroundColor Green
        Write-Host "==========================================" -ForegroundColor Green
        Write-Host ""
        
        # Find APK file
        $apkPath = "app\build\outputs\apk\release\app-release.apk"
        if (Test-Path $apkPath) {
            Write-Host "APK created successfully!" -ForegroundColor Green
            Write-Host "Location: $apkPath" -ForegroundColor Cyan
            
            # Show file size
            $apkFile = Get-Item $apkPath
            $sizeMB = [math]::Round($apkFile.Length / 1MB, 2)
            Write-Host "Size: $($apkFile.Length) bytes (~$sizeMB MB)" -ForegroundColor Cyan
            
            Write-Host ""
            Write-Host "Installation command:" -ForegroundColor Yellow
            Write-Host "  adb install $apkPath" -ForegroundColor White
            Write-Host ""
            Write-Host "Next steps:" -ForegroundColor Yellow
            Write-Host "  1. Connect Android device via USB" -ForegroundColor White
            Write-Host "  2. Enable USB debugging in Developer Options" -ForegroundColor White
            Write-Host "  3. Run: adb install $apkPath" -ForegroundColor White
            Write-Host "  4. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)" -ForegroundColor White
            Write-Host "  5. Enable accessibility service in Android settings" -ForegroundColor White
            
        } else {
            Write-Host "APK not found in expected location" -ForegroundColor Yellow
            Write-Host "Searching for APK files..." -ForegroundColor Yellow
            Get-ChildItem -Recurse -Filter "*.apk" | ForEach-Object {
                Write-Host "Found APK: $($_.FullName)" -ForegroundColor Cyan
            }
        }
        
    } else {
        Write-Host ""
        Write-Host "==========================================" -ForegroundColor Red
        Write-Host "BUILD FAILED" -ForegroundColor Red
        Write-Host "==========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "Common solutions:" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "1. Missing Android SDK:" -ForegroundColor White
        Write-Host "   - Install Android Studio" -ForegroundColor Gray
        Write-Host "   - Set ANDROID_HOME environment variable" -ForegroundColor Gray
        Write-Host ""
        Write-Host "2. Network issues:" -ForegroundColor White
        Write-Host "   - Check internet connection" -ForegroundColor Gray
        Write-Host "   - Try again (dependencies may be downloading)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "3. Java issues:" -ForegroundColor White
        Write-Host "   - Run: .\setup-java-env.bat" -ForegroundColor Gray
        Write-Host "   - Ensure JDK 11+ is installed" -ForegroundColor Gray
        Write-Host ""
        Write-Host "4. Disk space:" -ForegroundColor White
        Write-Host "   - Ensure at least 5GB free space" -ForegroundColor Gray
        Write-Host ""
        Write-Host "For detailed error analysis, check the output above." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Build failed with exception: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Build process completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
