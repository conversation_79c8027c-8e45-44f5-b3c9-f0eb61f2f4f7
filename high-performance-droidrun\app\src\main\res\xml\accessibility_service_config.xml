<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows|flagReportViewIds|flagRequestTouchExplorationMode"
    android:canRetrieveWindowContent="true"
    android:canRequestTouchExplorationMode="true"
    android:canPerformGestures="true"
    android:notificationTimeout="0"
    android:packageNames="@null"
    android:description="@string/accessibility_service_description"
    android:settingsActivity="com.droidrun.hp.ui.SettingsActivity" />
