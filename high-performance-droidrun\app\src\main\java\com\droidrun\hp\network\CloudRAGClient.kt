package com.droidrun.hp.network

import com.droidrun.hp.data.model.*
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 云端RAG客户端
 * 连接云端向量数据库和知识库服务
 */
@Singleton
class CloudRAGClient @Inject constructor() {
    
    companion object {
        private const val TIMEOUT_SECONDS = 15L
        private const val MAX_RETRIES = 2
        private const val CACHE_SIZE = 50
        private const val CACHE_TTL_MS = 300000L // 5分钟缓存
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .addInterceptor(CacheInterceptor())
        .build()
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    // 响应缓存
    private val queryCache = LRUCache<String, CachedRAGResult>(CACHE_SIZE)
    
    /**
     * 查询云端RAG
     */
    suspend fun queryRAG(query: RAGQuery): RAGResult {
        return PerformanceProfiler.measureExecutionTimeAsync("CloudRAGClient.queryRAG") {
            // 检查缓存
            val cacheKey = generateCacheKey(query)
            val cachedResult = queryCache.get(cacheKey)
            
            if (cachedResult != null && !cachedResult.isExpired()) {
                Timber.d("RAG查询命中缓存: ${query.text}")
                return@measureExecutionTimeAsync cachedResult.result
            }
            
            // 执行实际查询
            val result = performRAGQuery(query)
            
            // 缓存结果
            queryCache.put(cacheKey, CachedRAGResult(result, System.currentTimeMillis() + CACHE_TTL_MS))
            
            result
        }
    }
    
    /**
     * 批量查询RAG
     */
    suspend fun batchQueryRAG(queries: List<RAGQuery>): List<RAGResult> {
        return withContext(Dispatchers.IO) {
            queries.map { query ->
                async { queryRAG(query) }
            }.awaitAll()
        }
    }
    
    /**
     * 执行RAG查询
     */
    private suspend fun performRAGQuery(query: RAGQuery): RAGResult {
        return when (query.provider) {
            RAGProvider.PINECONE -> queryPinecone(query)
            RAGProvider.WEAVIATE -> queryWeaviate(query)
            RAGProvider.QDRANT -> queryQdrant(query)
            RAGProvider.CUSTOM -> queryCustomRAG(query)
        }
    }
    
    /**
     * 查询Pinecone
     */
    private suspend fun queryPinecone(query: RAGQuery): RAGResult {
        val requestBody = buildPineconeRequest(query)
        val request = Request.Builder()
            .url("${query.baseUrl}/query")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("Api-Key", query.apiKey)
            .addHeader("Content-Type", "application/json")
            .build()
        
        return executeRAGRequest(request, query) { responseBody ->
            parsePineconeResponse(responseBody, query)
        }
    }
    
    /**
     * 查询Weaviate
     */
    private suspend fun queryWeaviate(query: RAGQuery): RAGResult {
        val requestBody = buildWeaviateRequest(query)
        val request = Request.Builder()
            .url("${query.baseUrl}/v1/graphql")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("Authorization", "Bearer ${query.apiKey}")
            .addHeader("Content-Type", "application/json")
            .build()
        
        return executeRAGRequest(request, query) { responseBody ->
            parseWeaviateResponse(responseBody, query)
        }
    }
    
    /**
     * 查询Qdrant
     */
    private suspend fun queryQdrant(query: RAGQuery): RAGResult {
        val requestBody = buildQdrantRequest(query)
        val request = Request.Builder()
            .url("${query.baseUrl}/collections/${query.collection}/points/search")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .addHeader("api-key", query.apiKey)
            .addHeader("Content-Type", "application/json")
            .build()
        
        return executeRAGRequest(request, query) { responseBody ->
            parseQdrantResponse(responseBody, query)
        }
    }
    
    /**
     * 查询自定义RAG服务
     */
    private suspend fun queryCustomRAG(query: RAGQuery): RAGResult {
        // 自定义RAG实现
        return RAGResult(
            query = query.text,
            results = listOf(
                RAGResult.RelevantEntry(
                    entry = KnowledgeEntry(
                        id = "custom_1",
                        title = "自定义知识条目",
                        content = "这是一个自定义的知识库响应",
                        category = "custom"
                    ),
                    similarity = 0.8f,
                    relevanceScore = 0.8f
                )
            ),
            context = "自定义RAG服务响应",
            processingTimeMs = 100
        )
    }
    
    /**
     * 执行RAG请求
     */
    private suspend fun <T> executeRAGRequest(
        request: Request,
        query: RAGQuery,
        parser: (String) -> T
    ): T {
        return withContext(Dispatchers.IO) {
            var lastException: Exception? = null
            
            repeat(MAX_RETRIES) { attempt ->
                try {
                    val response = httpClient.newCall(request).execute()
                    
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string() ?: ""
                        return@withContext parser(responseBody)
                    } else {
                        throw RAGException("HTTP ${response.code}: ${response.message}")
                    }
                    
                } catch (e: Exception) {
                    lastException = e
                    Timber.w("RAG查询失败 (尝试 ${attempt + 1}/$MAX_RETRIES): ${e.message}")
                    
                    if (attempt < MAX_RETRIES - 1) {
                        delay(1000L * (attempt + 1))
                    }
                }
            }
            
            throw lastException ?: Exception("RAG查询失败")
        }
    }
    
    /**
     * 构建Pinecone请求
     */
    private fun buildPineconeRequest(query: RAGQuery): String {
        return json.encodeToString(
            PineconeRequest.serializer(),
            PineconeRequest(
                vector = query.embedding ?: emptyList(),
                topK = query.maxResults ?: 5,
                includeMetadata = true,
                includeValues = false,
                filter = query.filters
            )
        )
    }
    
    /**
     * 构建Weaviate请求
     */
    private fun buildWeaviateRequest(query: RAGQuery): String {
        val graphqlQuery = """
            {
                Get {
                    ${query.collection}(
                        nearText: {
                            concepts: ["${query.text}"]
                        }
                        limit: ${query.maxResults ?: 5}
                    ) {
                        title
                        content
                        category
                        _additional {
                            certainty
                        }
                    }
                }
            }
        """.trimIndent()
        
        return json.encodeToString(
            WeaviateRequest.serializer(),
            WeaviateRequest(query = graphqlQuery)
        )
    }
    
    /**
     * 构建Qdrant请求
     */
    private fun buildQdrantRequest(query: RAGQuery): String {
        return json.encodeToString(
            QdrantRequest.serializer(),
            QdrantRequest(
                vector = query.embedding ?: emptyList(),
                limit = query.maxResults ?: 5,
                withPayload = true,
                withVector = false,
                scoreThreshold = query.similarityThreshold ?: 0.7f
            )
        )
    }
    
    /**
     * 解析Pinecone响应
     */
    private fun parsePineconeResponse(responseBody: String, query: RAGQuery): RAGResult {
        val response = json.decodeFromString<PineconeResponse>(responseBody)
        
        val results = response.matches.map { match ->
            RAGResult.RelevantEntry(
                entry = KnowledgeEntry(
                    id = match.id,
                    title = match.metadata["title"] as? String ?: "",
                    content = match.metadata["content"] as? String ?: "",
                    category = match.metadata["category"] as? String ?: ""
                ),
                similarity = match.score,
                relevanceScore = match.score
            )
        }
        
        return RAGResult(
            query = query.text,
            results = results,
            context = generateContext(results),
            processingTimeMs = 0
        )
    }
    
    /**
     * 解析Weaviate响应
     */
    private fun parseWeaviateResponse(responseBody: String, query: RAGQuery): RAGResult {
        val response = json.decodeFromString<WeaviateResponse>(responseBody)
        
        val results = response.data.Get[query.collection]?.map { item ->
            RAGResult.RelevantEntry(
                entry = KnowledgeEntry(
                    id = item.id ?: "",
                    title = item.title ?: "",
                    content = item.content ?: "",
                    category = item.category ?: ""
                ),
                similarity = item._additional?.certainty ?: 0f,
                relevanceScore = item._additional?.certainty ?: 0f
            )
        } ?: emptyList()
        
        return RAGResult(
            query = query.text,
            results = results,
            context = generateContext(results),
            processingTimeMs = 0
        )
    }
    
    /**
     * 解析Qdrant响应
     */
    private fun parseQdrantResponse(responseBody: String, query: RAGQuery): RAGResult {
        val response = json.decodeFromString<QdrantResponse>(responseBody)
        
        val results = response.result.map { point ->
            RAGResult.RelevantEntry(
                entry = KnowledgeEntry(
                    id = point.id.toString(),
                    title = point.payload["title"] as? String ?: "",
                    content = point.payload["content"] as? String ?: "",
                    category = point.payload["category"] as? String ?: ""
                ),
                similarity = point.score,
                relevanceScore = point.score
            )
        }
        
        return RAGResult(
            query = query.text,
            results = results,
            context = generateContext(results),
            processingTimeMs = 0
        )
    }
    
    /**
     * 生成上下文
     */
    private fun generateContext(results: List<RAGResult.RelevantEntry>): String {
        return results.take(3).joinToString("\n\n") { entry ->
            "${entry.entry.title}: ${entry.entry.content}"
        }
    }
    
    /**
     * 生成缓存键
     */
    private fun generateCacheKey(query: RAGQuery): String {
        return "${query.text}_${query.category}_${query.maxResults}".hashCode().toString()
    }
    
    /**
     * 缓存的RAG结果
     */
    private data class CachedRAGResult(
        val result: RAGResult,
        val expiryTime: Long
    ) {
        fun isExpired(): Boolean = System.currentTimeMillis() > expiryTime
    }
    
    /**
     * 缓存拦截器
     */
    private class CacheInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val response = chain.proceed(request)
            
            // 为GET请求添加缓存头
            if (request.method == "GET") {
                return response.newBuilder()
                    .header("Cache-Control", "public, max-age=300")
                    .build()
            }
            
            return response
        }
    }
    
    /**
     * RAG异常
     */
    class RAGException(message: String) : Exception(message)
}
