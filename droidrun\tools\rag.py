"""
DroidRun Tools - RAG Knowledge Base Query.
"""

import requests
import json

def query_rag(question):
    """
    Query the local RAG knowledge base with a question.
    
    Args:
        question (str): The question to ask the RAG system.
        
    Returns:
        str: The answer from the RAG system.
    """
    url = "http://127.0.0.1:8002/ask"
    headers = {"Content-Type": "application/json"}
    data = {"question": question}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            return response.json()["answer"]
        else:
            return f"Error: {response.status_code}, {response.text}"
    except Exception as e:
        return f"Error connecting to RAG service: {str(e)}" 