package com.droidrun.hp.core.engine

import android.content.Context
import com.droidrun.hp.data.model.KnowledgeEntry
import com.droidrun.hp.data.model.RAGQuery
import com.droidrun.hp.data.model.RAGResult
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 本地RAG（检索增强生成）引擎
 * 提供高速的本地知识库查询和向量搜索
 */
@Singleton
class LocalRAGEngine @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val KNOWLEDGE_DIR = "knowledge"
        private const val VECTOR_CACHE_SIZE = 10000
        private const val SIMILARITY_THRESHOLD = 0.7f
        private const val MAX_RESULTS = 10
        
        // 加载原生库
        init {
            try {
                System.loadLibrary("droidrun_hp")
                Timber.d("RAG引擎原生库加载成功")
            } catch (e: UnsatisfiedLinkError) {
                Timber.e(e, "RAG引擎原生库加载失败")
            }
        }
    }
    
    // 引擎状态
    private val isInitialized = AtomicBoolean(false)
    private var vectorDBHandle: Long = 0L
    
    // 知识库
    private val knowledgeBase = ConcurrentHashMap<String, KnowledgeEntry>()
    private val vectorCache = ConcurrentHashMap<String, FloatArray>()
    
    // 性能统计
    private val queryCounter = AtomicLong(0)
    private val totalQueryTime = AtomicLong(0)
    
    // 嵌入模型
    private var embeddingModelHandle: Long = 0L
    
    /**
     * 初始化RAG引擎
     */
    suspend fun initialize() {
        if (isInitialized.get()) {
            Timber.w("RAG引擎已经初始化")
            return
        }
        
        try {
            Timber.i("开始初始化RAG引擎...")
            
            PerformanceProfiler.measureExecutionTime("RAGEngine.Initialize") {
                // 1. 初始化向量数据库
                vectorDBHandle = initializeVectorDB(VECTOR_CACHE_SIZE)
                if (vectorDBHandle == 0L) {
                    throw RuntimeException("初始化向量数据库失败")
                }
                
                // 2. 初始化嵌入模型
                val embeddingModelPath = prepareEmbeddingModel()
                embeddingModelHandle = initializeEmbeddingModel(embeddingModelPath)
                if (embeddingModelHandle == 0L) {
                    throw RuntimeException("初始化嵌入模型失败")
                }
                
                // 3. 加载知识库
                loadKnowledgeBase()
                
                // 4. 构建向量索引
                buildVectorIndex()
                
                isInitialized.set(true)
                Timber.i("RAG引擎初始化完成，知识条目数: ${knowledgeBase.size}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "RAG引擎初始化失败")
            throw e
        }
    }
    
    /**
     * 执行RAG查询
     */
    suspend fun query(query: RAGQuery): RAGResult {
        if (!isInitialized.get()) {
            throw IllegalStateException("RAG引擎未初始化")
        }
        
        return withContext(Dispatchers.Default) {
            val startTime = System.currentTimeMillis()
            
            val result = PerformanceProfiler.measureExecutionTime("RAGEngine.Query") {
                performRAGQuery(query)
            }
            
            val queryTime = System.currentTimeMillis() - startTime
            
            // 更新统计信息
            queryCounter.incrementAndGet()
            totalQueryTime.addAndGet(queryTime)
            
            Timber.d("RAG查询完成，耗时: ${queryTime}ms, 结果数: ${result.results.size}")
            
            result
        }
    }
    
    /**
     * 批量查询
     */
    suspend fun batchQuery(queries: List<RAGQuery>): List<RAGResult> {
        return withContext(Dispatchers.Default) {
            queries.map { query ->
                async { query(query) }
            }.awaitAll()
        }
    }
    
    /**
     * 添加知识条目
     */
    suspend fun addKnowledge(entry: KnowledgeEntry) {
        withContext(Dispatchers.Default) {
            // 计算嵌入向量
            val embedding = computeEmbedding(entry.content)
            
            // 添加到知识库
            knowledgeBase[entry.id] = entry
            vectorCache[entry.id] = embedding
            
            // 添加到向量数据库
            addVectorToDB(vectorDBHandle, entry.id, embedding)
            
            Timber.d("添加知识条目: ${entry.id}")
        }
    }
    
    /**
     * 批量添加知识条目
     */
    suspend fun addKnowledgeBatch(entries: List<KnowledgeEntry>) {
        withContext(Dispatchers.Default) {
            entries.forEach { entry ->
                addKnowledge(entry)
            }
        }
    }
    
    /**
     * 更新知识条目
     */
    suspend fun updateKnowledge(entry: KnowledgeEntry) {
        withContext(Dispatchers.Default) {
            // 删除旧的向量
            removeVectorFromDB(vectorDBHandle, entry.id)
            
            // 添加新的向量
            addKnowledge(entry)
            
            Timber.d("更新知识条目: ${entry.id}")
        }
    }
    
    /**
     * 删除知识条目
     */
    suspend fun removeKnowledge(entryId: String) {
        withContext(Dispatchers.Default) {
            knowledgeBase.remove(entryId)
            vectorCache.remove(entryId)
            removeVectorFromDB(vectorDBHandle, entryId)
            
            Timber.d("删除知识条目: $entryId")
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        if (isInitialized.get()) {
            releaseVectorDB(vectorDBHandle)
            releaseEmbeddingModel(embeddingModelHandle)
            
            vectorDBHandle = 0L
            embeddingModelHandle = 0L
            
            knowledgeBase.clear()
            vectorCache.clear()
            
            isInitialized.set(false)
            
            Timber.i("RAG引擎资源已释放")
        }
    }
    
    /**
     * 获取引擎状态
     */
    fun getStatus(): String {
        return if (isInitialized.get()) {
            val avgQueryTime = if (queryCounter.get() > 0) {
                totalQueryTime.get() / queryCounter.get()
            } else 0L
            
            "已初始化 - 知识条目: ${knowledgeBase.size}, " +
            "查询次数: ${queryCounter.get()}, " +
            "平均耗时: ${avgQueryTime}ms"
        } else {
            "未初始化"
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): RAGPerformanceStats {
        return RAGPerformanceStats(
            isInitialized = isInitialized.get(),
            knowledgeEntryCount = knowledgeBase.size,
            vectorCacheSize = vectorCache.size,
            queryCount = queryCounter.get(),
            totalQueryTime = totalQueryTime.get(),
            averageQueryTime = if (queryCounter.get() > 0) {
                totalQueryTime.get() / queryCounter.get()
            } else 0L
        )
    }
    
    /**
     * 执行RAG查询的核心逻辑
     */
    private suspend fun performRAGQuery(query: RAGQuery): RAGResult {
        // 1. 计算查询向量
        val queryEmbedding = computeEmbedding(query.text)
        
        // 2. 向量相似度搜索
        val similarVectors = searchSimilarVectors(
            vectorDBHandle, 
            queryEmbedding, 
            query.maxResults ?: MAX_RESULTS,
            query.similarityThreshold ?: SIMILARITY_THRESHOLD
        )
        
        // 3. 获取相关知识条目
        val relevantEntries = similarVectors.mapNotNull { (entryId, similarity) ->
            knowledgeBase[entryId]?.let { entry ->
                RAGResult.RelevantEntry(
                    entry = entry,
                    similarity = similarity,
                    relevanceScore = calculateRelevanceScore(query, entry, similarity)
                )
            }
        }.sortedByDescending { it.relevanceScore }
        
        // 4. 生成上下文
        val context = generateContext(relevantEntries, query.contextLength ?: 2048)
        
        return RAGResult(
            query = query.text,
            results = relevantEntries,
            context = context,
            processingTimeMs = 0L // 会在外层计算
        )
    }
    
    /**
     * 计算文本嵌入向量
     */
    private suspend fun computeEmbedding(text: String): FloatArray {
        return withContext(Dispatchers.Default) {
            // 检查缓存
            val cacheKey = text.hashCode().toString()
            vectorCache[cacheKey]?.let { return@withContext it }
            
            // 计算新的嵌入向量
            val embedding = computeEmbeddingNative(embeddingModelHandle, text)
            
            // 缓存结果
            if (vectorCache.size < VECTOR_CACHE_SIZE) {
                vectorCache[cacheKey] = embedding
            }
            
            embedding
        }
    }
    
    /**
     * 加载知识库
     */
    private suspend fun loadKnowledgeBase() {
        withContext(Dispatchers.IO) {
            val knowledgeFiles = listOf(
                "android_ui_patterns.json",
                "common_operations.json",
                "app_specific_guides.json",
                "troubleshooting_guides.json"
            )
            
            knowledgeFiles.forEach { fileName ->
                try {
                    val jsonContent = context.assets.open("$KNOWLEDGE_DIR/$fileName").bufferedReader().use { it.readText() }
                    val entries: List<KnowledgeEntry> = Json.decodeFromString(jsonContent)
                    
                    entries.forEach { entry ->
                        knowledgeBase[entry.id] = entry
                    }
                    
                    Timber.d("加载知识文件: $fileName, 条目数: ${entries.size}")
                    
                } catch (e: Exception) {
                    Timber.e(e, "加载知识文件失败: $fileName")
                }
            }
        }
    }
    
    /**
     * 构建向量索引
     */
    private suspend fun buildVectorIndex() {
        withContext(Dispatchers.Default) {
            Timber.i("开始构建向量索引...")
            
            val entries = knowledgeBase.values.toList()
            val batchSize = 100
            
            entries.chunked(batchSize).forEach { batch ->
                val embeddings = batch.map { entry ->
                    entry.id to computeEmbedding(entry.content)
                }
                
                // 批量添加到向量数据库
                addVectorsBatchToDB(vectorDBHandle, embeddings)
            }
            
            Timber.i("向量索引构建完成")
        }
    }
    
    /**
     * 准备嵌入模型
     */
    private suspend fun prepareEmbeddingModel(): String {
        val modelFileName = "sentence-transformer.onnx"
        val modelFile = File(context.filesDir, "models/$modelFileName")
        
        if (!modelFile.exists()) {
            withContext(Dispatchers.IO) {
                modelFile.parentFile?.mkdirs()
                context.assets.open("models/$modelFileName").use { input ->
                    modelFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
            }
        }
        
        return modelFile.absolutePath
    }
    
    /**
     * 计算相关性分数
     */
    private fun calculateRelevanceScore(
        query: RAGQuery, 
        entry: KnowledgeEntry, 
        similarity: Float
    ): Float {
        var score = similarity
        
        // 根据类别匹配度调整分数
        if (query.category != null && entry.category == query.category) {
            score *= 1.2f
        }
        
        // 根据标签匹配度调整分数
        val commonTags = query.tags?.intersect(entry.tags ?: emptySet())?.size ?: 0
        if (commonTags > 0) {
            score *= (1.0f + commonTags * 0.1f)
        }
        
        // 根据重要性调整分数
        score *= entry.importance
        
        return minOf(score, 1.0f)
    }
    
    /**
     * 生成上下文
     */
    private fun generateContext(entries: List<RAGResult.RelevantEntry>, maxLength: Int): String {
        val context = StringBuilder()
        var currentLength = 0
        
        for (entry in entries) {
            val entryText = "${entry.entry.title}: ${entry.entry.content}\n\n"
            if (currentLength + entryText.length > maxLength) {
                break
            }
            
            context.append(entryText)
            currentLength += entryText.length
        }
        
        return context.toString().trim()
    }
    
    // 原生方法声明
    private external fun initializeVectorDB(cacheSize: Int): Long
    private external fun initializeEmbeddingModel(modelPath: String): Long
    private external fun computeEmbeddingNative(modelHandle: Long, text: String): FloatArray
    private external fun addVectorToDB(dbHandle: Long, id: String, vector: FloatArray)
    private external fun addVectorsBatchToDB(dbHandle: Long, vectors: List<Pair<String, FloatArray>>)
    private external fun removeVectorFromDB(dbHandle: Long, id: String)
    private external fun searchSimilarVectors(
        dbHandle: Long, 
        queryVector: FloatArray, 
        maxResults: Int, 
        threshold: Float
    ): List<Pair<String, Float>>
    private external fun releaseVectorDB(dbHandle: Long)
    private external fun releaseEmbeddingModel(modelHandle: Long)
}

/**
 * RAG性能统计
 */
data class RAGPerformanceStats(
    val isInitialized: Boolean,
    val knowledgeEntryCount: Int,
    val vectorCacheSize: Int,
    val queryCount: Long,
    val totalQueryTime: Long,
    val averageQueryTime: Long
)
