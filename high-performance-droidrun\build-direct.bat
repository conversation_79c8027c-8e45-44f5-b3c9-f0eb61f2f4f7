@echo off
echo DroidRun Direct Build (Bypass Gradle Wrapper)
echo ==============================================

echo Checking for system Gradle...
where gradle >nul 2>&1
if %errorlevel% neq 0 (
    echo System Gradle not found. Trying common locations...
    
    REM Check common Gradle installation paths
    if exist "C:\gradle\gradle-8.4\bin\gradle.bat" (
        set GRADLE_CMD=C:\gradle\gradle-8.4\bin\gradle.bat
        echo Found Gradle at: C:\gradle\gradle-8.4\bin\gradle.bat
    ) else if exist "C:\Program Files\Gradle\gradle-8.4\bin\gradle.bat" (
        set GRADLE_CMD=C:\Program Files\Gradle\gradle-8.4\bin\gradle.bat
        echo Found Gradle at: C:\Program Files\Gradle\gradle-8.4\bin\gradle.bat
    ) else (
        echo No Gradle installation found.
        echo.
        echo Options:
        echo   1. Run .\install-gradle.bat to install Gradle
        echo   2. Install Android Studio (includes Gradle)
        echo   3. Download Gradle manually from https://gradle.org/releases/
        echo.
        pause
        exit /b 1
    )
) else (
    set GRADLE_CMD=gradle
    echo OK: System Gradle found
)

echo.
echo Using Gradle command: %GRADLE_CMD%
echo.

REM Verify Gradle works
echo Testing Gradle...
%GRADLE_CMD% --version
if %errorlevel% neq 0 (
    echo ERROR: Gradle command failed
    pause
    exit /b 1
)

echo.
echo Setting up Android environment...

REM Try to find Android SDK
if not defined ANDROID_HOME (
    echo ANDROID_HOME not set. Searching for Android SDK...
    
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
        echo Found Android SDK: %ANDROID_HOME%
    ) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
        echo Found Android SDK: %ANDROID_HOME%
    ) else if exist "C:\Android\Sdk" (
        set ANDROID_HOME=C:\Android\Sdk
        echo Found Android SDK: %ANDROID_HOME%
    ) else (
        echo WARNING: Android SDK not found
        echo Please install Android Studio or set ANDROID_HOME manually
        echo Build may fail without proper Android SDK
    )
) else (
    echo Using ANDROID_HOME: %ANDROID_HOME%
)

echo.
echo Starting build process...
echo =========================

echo Step 1: Cleaning project...
%GRADLE_CMD% clean
if %errorlevel% neq 0 (
    echo Clean failed, but continuing...
)

echo.
echo Step 2: Building release APK...
%GRADLE_CMD% assembleRelease --stacktrace --info

if %errorlevel% equ 0 (
    echo.
    echo ==========================================
    echo BUILD COMPLETED SUCCESSFULLY!
    echo ==========================================
    echo.
    
    REM Find and display APK information
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK Location: app\build\outputs\apk\release\app-release.apk
        
        REM Get file size
        for %%A in ("app\build\outputs\apk\release\app-release.apk") do (
            echo APK Size: %%~zA bytes
            set /a size_mb=%%~zA/1024/1024
            echo APK Size: !size_mb! MB
        )
        
        echo.
        echo Installation command:
        echo   adb install app\build\outputs\apk\release\app-release.apk
        echo.
        echo Next steps:
        echo   1. Connect your Android device via USB
        echo   2. Enable USB debugging in Developer Options
        echo   3. Run the installation command above
        echo   4. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)
        
    ) else (
        echo APK not found in expected location. Searching...
        echo.
        echo Looking for APK files:
        for /r %%i in (*.apk) do (
            echo Found: %%i
        )
    )
    
) else (
    echo.
    echo ==========================================
    echo BUILD FAILED
    echo ==========================================
    echo.
    echo The build failed. Common causes:
    echo.
    echo 1. Missing Android SDK
    echo    Solution: Install Android Studio
    echo.
    echo 2. Network issues
    echo    Solution: Check internet connection
    echo.
    echo 3. Java version issues
    echo    Solution: Install JDK 11 or higher
    echo.
    echo 4. Insufficient disk space
    echo    Solution: Free up disk space
    echo.
    echo 5. Antivirus interference
    echo    Solution: Temporarily disable antivirus
    echo.
    echo For detailed logs, check the output above.
    echo For more help, see BUILD_GUIDE.md
)

echo.
echo Press any key to exit...
pause >nul
