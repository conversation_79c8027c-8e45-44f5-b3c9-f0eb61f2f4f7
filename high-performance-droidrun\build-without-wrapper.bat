@echo off
echo DroidRun Build (Without Gradle Wrapper)
echo ========================================

echo Checking if system Gradle is available...
gradle --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: System Gradle not found
    echo.
    echo Solutions:
    echo 1. Install Gradle: https://gradle.org/install/
    echo 2. Use Android Studio to build the project
    echo 3. Fix Gradle wrapper: .\fix-gradle.bat
    echo.
    pause
    exit /b 1
)

echo OK: System Gradle found
echo.

echo Setting ANDROID_HOME if not set...
if not defined ANDROID_HOME (
    REM Try common Android SDK locations
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
        echo Set ANDROID_HOME to: %ANDROID_HOME%
    ) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
        echo Set ANDROID_HOME to: %ANDROID_HOME%
    ) else (
        echo WARNING: Could not find Android SDK automatically
        echo Please install Android Studio or set ANDROID_HOME manually
    )
)

echo.
echo Building with system Gradle...
echo ==============================

REM Clean first
echo Cleaning project...
gradle clean

REM Build release
echo Building release APK...
gradle assembleRelease

if %errorlevel% equ 0 (
    echo.
    echo ==============================
    echo BUILD SUCCESS!
    echo.
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK created: app\build\outputs\apk\release\app-release.apk
        echo File size:
        dir "app\build\outputs\apk\release\app-release.apk" | find "app-release.apk"
    ) else (
        echo APK file not found in expected location
        echo Searching for APK files...
        dir /s *.apk | find ".apk"
    )
    echo.
    echo Next steps:
    echo 1. Install: adb install app\build\outputs\apk\release\app-release.apk
    echo 2. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)
) else (
    echo.
    echo ==============================
    echo BUILD FAILED
    echo.
    echo Try these solutions:
    echo 1. Check internet connection
    echo 2. Update Android SDK
    echo 3. Use Android Studio to build
)

echo.
pause
