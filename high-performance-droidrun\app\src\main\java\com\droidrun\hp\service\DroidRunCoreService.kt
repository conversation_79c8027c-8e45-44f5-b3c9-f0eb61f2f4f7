package com.droidrun.hp.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.droidrun.hp.R
import com.droidrun.hp.core.agent.NativeAgentEngine
import com.droidrun.hp.core.engine.LocalLLMEngine
import com.droidrun.hp.core.engine.LocalRAGEngine
import com.droidrun.hp.data.repository.TaskRepository
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject

/**
 * DroidRun核心后台服务
 * 负责管理所有AI引擎和执行任务
 */
class DroidRunCoreService : Service() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "droidrun_core_service"
        private const val CHANNEL_NAME = "DroidRun Core Service"
        
        const val ACTION_START_TASK = "com.droidrun.hp.START_TASK"
        const val ACTION_STOP_TASK = "com.droidrun.hp.STOP_TASK"
        const val ACTION_GET_STATUS = "com.droidrun.hp.GET_STATUS"
        
        const val EXTRA_TASK_COMMAND = "task_command"
        const val EXTRA_TASK_ID = "task_id"
    }
    
    // 服务绑定器
    inner class DroidRunBinder : Binder() {
        fun getService(): DroidRunCoreService = this@DroidRunCoreService
    }
    
    private val binder = DroidRunBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    
    // 核心引擎组件
    @Inject
    lateinit var llmEngine: LocalLLMEngine
    
    @Inject
    lateinit var ragEngine: LocalRAGEngine
    
    @Inject
    lateinit var agentEngine: NativeAgentEngine
    
    @Inject
    lateinit var taskRepository: TaskRepository
    
    // 服务状态
    private var isInitialized = false
    private var currentTaskId: String? = null
    private val runningTasks = mutableMapOf<String, Job>()
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("DroidRunCoreService onCreate")
        
        // 创建通知渠道
        createNotificationChannel()
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification("DroidRun服务启动中..."))
        
        // 异步初始化核心组件
        serviceScope.launch {
            initializeCoreComponents()
        }
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("DroidRunCoreService onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_TASK -> {
                val command = intent.getStringExtra(EXTRA_TASK_COMMAND)
                val taskId = intent.getStringExtra(EXTRA_TASK_ID)
                if (command != null && taskId != null) {
                    startTask(taskId, command)
                }
            }
            ACTION_STOP_TASK -> {
                val taskId = intent.getStringExtra(EXTRA_TASK_ID)
                if (taskId != null) {
                    stopTask(taskId)
                }
            }
            ACTION_GET_STATUS -> {
                // 广播当前状态
                broadcastStatus()
            }
        }
        
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder = binder
    
    override fun onDestroy() {
        super.onDestroy()
        Timber.d("DroidRunCoreService onDestroy")
        
        // 取消所有运行中的任务
        runningTasks.values.forEach { it.cancel() }
        runningTasks.clear()
        
        // 清理资源
        serviceScope.cancel()
        
        // 释放引擎资源
        if (::llmEngine.isInitialized) {
            llmEngine.release()
        }
        if (::ragEngine.isInitialized) {
            ragEngine.release()
        }
    }
    
    /**
     * 初始化核心组件
     */
    private suspend fun initializeCoreComponents() {
        try {
            updateNotification("正在初始化AI引擎...")
            
            PerformanceProfiler.measureExecutionTime("CoreService.Initialize") {
                // 并行初始化各个引擎
                val initJobs = listOf(
                    async { llmEngine.initialize() },
                    async { ragEngine.initialize() },
                    async { agentEngine.initialize() }
                )
                
                // 等待所有引擎初始化完成
                initJobs.awaitAll()
            }
            
            isInitialized = true
            updateNotification("DroidRun服务就绪")
            
            Timber.i("DroidRun核心服务初始化完成")
            
        } catch (e: Exception) {
            Timber.e(e, "核心服务初始化失败")
            updateNotification("服务初始化失败: ${e.message}")
            
            // 延迟重试初始化
            delay(5000)
            initializeCoreComponents()
        }
    }
    
    /**
     * 启动任务执行
     */
    fun startTask(taskId: String, command: String) {
        if (!isInitialized) {
            Timber.w("服务未初始化完成，任务将排队等待")
            // 可以实现任务队列机制
            return
        }
        
        // 取消同ID的现有任务
        runningTasks[taskId]?.cancel()
        
        val taskJob = serviceScope.launch {
            try {
                currentTaskId = taskId
                updateNotification("正在执行任务: $command")
                
                Timber.i("开始执行任务 [$taskId]: $command")
                
                // 使用Agent引擎执行任务
                val result = PerformanceProfiler.measureExecutionTime("Task.Execute") {
                    agentEngine.executeTask(taskId, command)
                }
                
                // 保存任务结果
                taskRepository.saveTaskResult(taskId, result)
                
                Timber.i("任务 [$taskId] 执行完成")
                updateNotification("任务执行完成")
                
                // 广播任务完成事件
                broadcastTaskComplete(taskId, result.success)
                
            } catch (e: CancellationException) {
                Timber.i("任务 [$taskId] 被取消")
                updateNotification("任务已取消")
                broadcastTaskCancelled(taskId)
            } catch (e: Exception) {
                Timber.e(e, "任务 [$taskId] 执行失败")
                updateNotification("任务执行失败: ${e.message}")
                broadcastTaskError(taskId, e.message ?: "未知错误")
            } finally {
                currentTaskId = null
                runningTasks.remove(taskId)
            }
        }
        
        runningTasks[taskId] = taskJob
    }
    
    /**
     * 停止任务执行
     */
    fun stopTask(taskId: String) {
        runningTasks[taskId]?.cancel()
        runningTasks.remove(taskId)
        
        if (currentTaskId == taskId) {
            currentTaskId = null
            updateNotification("DroidRun服务就绪")
        }
        
        Timber.i("任务 [$taskId] 已停止")
    }
    
    /**
     * 获取服务状态
     */
    fun getServiceStatus(): ServiceStatus {
        return ServiceStatus(
            isInitialized = isInitialized,
            currentTaskId = currentTaskId,
            runningTaskCount = runningTasks.size,
            llmEngineStatus = if (::llmEngine.isInitialized) llmEngine.getStatus() else "未初始化",
            ragEngineStatus = if (::ragEngine.isInitialized) ragEngine.getStatus() else "未初始化"
        )
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "DroidRun核心服务通知"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(content: String): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("DroidRun")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    /**
     * 更新通知内容
     */
    private fun updateNotification(content: String) {
        val notification = createNotification(content)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * 广播服务状态
     */
    private fun broadcastStatus() {
        val intent = Intent("com.droidrun.hp.SERVICE_STATUS").apply {
            putExtra("status", getServiceStatus())
        }
        sendBroadcast(intent)
    }
    
    /**
     * 广播任务完成事件
     */
    private fun broadcastTaskComplete(taskId: String, success: Boolean) {
        val intent = Intent("com.droidrun.hp.TASK_COMPLETE").apply {
            putExtra("task_id", taskId)
            putExtra("success", success)
        }
        sendBroadcast(intent)
    }
    
    /**
     * 广播任务取消事件
     */
    private fun broadcastTaskCancelled(taskId: String) {
        val intent = Intent("com.droidrun.hp.TASK_CANCELLED").apply {
            putExtra("task_id", taskId)
        }
        sendBroadcast(intent)
    }
    
    /**
     * 广播任务错误事件
     */
    private fun broadcastTaskError(taskId: String, error: String) {
        val intent = Intent("com.droidrun.hp.TASK_ERROR").apply {
            putExtra("task_id", taskId)
            putExtra("error", error)
        }
        sendBroadcast(intent)
    }
}

/**
 * 服务状态数据类
 */
data class ServiceStatus(
    val isInitialized: Boolean,
    val currentTaskId: String?,
    val runningTaskCount: Int,
    val llmEngineStatus: String,
    val ragEngineStatus: String
)
