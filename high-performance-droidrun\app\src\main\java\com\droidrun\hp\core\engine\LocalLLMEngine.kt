package com.droidrun.hp.core.engine

import android.content.Context
import com.droidrun.hp.data.model.LLMRequest
import com.droidrun.hp.data.model.LLMResponse
import com.droidrun.hp.network.LLMApiClient
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 轻量级LLM引擎
 * 调用外部LLM API服务，支持多种提供商
 */
@Singleton
class LightweightLLMEngine @Inject constructor(
    private val context: Context,
    private val apiClient: LLMApiClient
) {
    
    companion object {
        private const val MAX_TOKENS = 512
        private const val DEFAULT_TEMPERATURE = 0.2f
        private const val REQUEST_TIMEOUT_MS = 30000L
        private const val MAX_RETRIES = 3
        private const val CACHE_SIZE = 100
    }

    // 引擎状态
    private val isInitialized = AtomicBoolean(false)
    private val isRequesting = AtomicBoolean(false)

    // 性能统计
    private val requestCounter = AtomicLong(0)
    private val totalRequestTime = AtomicLong(0)
    private val cacheHitCounter = AtomicLong(0)

    // 响应缓存
    private val responseCache = LRUCache<String, LLMResponse>(CACHE_SIZE)

    // 请求队列和批处理
    private val requestQueue = mutableListOf<PendingRequest>()
    private val batchProcessor = CoroutineScope(
        SupervisorJob() +
        Dispatchers.IO +
        CoroutineName("LLMBatchProcessor")
    )

    // 当前配置
    private var currentProvider: LLMProvider = LLMProvider.OPENAI
    private var apiKey: String = ""
    private var baseUrl: String = ""
    
    /**
     * 初始化LLM引擎
     */
    suspend fun initialize() {
        if (isInitialized.get()) {
            Timber.w("LLM引擎已经初始化")
            return
        }
        
        try {
            Timber.i("开始初始化LLM引擎...")
            
            PerformanceProfiler.measureExecutionTime("LLMEngine.Initialize") {
                // 1. 选择最优模型配置
                currentModelConfig = selectOptimalModelConfig()
                Timber.i("选择模型配置: ${currentModelConfig}")
                
                // 2. 准备模型文件
                val modelPath = prepareModelFile(currentModelConfig!!.modelFileName)
                Timber.i("模型文件路径: $modelPath")
                
                // 3. 初始化推理引擎
                modelHandle = initializeNativeEngine(
                    modelPath,
                    currentModelConfig!!.contextLength,
                    currentModelConfig!!.threads
                )
                
                if (modelHandle == 0L) {
                    throw RuntimeException("初始化原生推理引擎失败")
                }
                
                // 4. 预热模型
                warmupModel()
                
                isInitialized.set(true)
                Timber.i("LLM引擎初始化完成")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "LLM引擎初始化失败")
            throw e
        }
    }
    
    /**
     * 执行推理
     */
    suspend fun inference(prompt: String, maxTokens: Int = MAX_TOKENS): String {
        if (!isInitialized.get()) {
            throw IllegalStateException("LLM引擎未初始化")
        }
        
        if (isInferencing.get()) {
            throw IllegalStateException("引擎正在推理中，请等待")
        }
        
        return withContext(Dispatchers.Default) {
            isInferencing.set(true)
            
            try {
                val startTime = System.currentTimeMillis()
                
                val result = PerformanceProfiler.measureExecutionTime("LLMEngine.Inference") {
                    performInference(modelHandle, prompt, maxTokens)
                }
                
                val inferenceTime = System.currentTimeMillis() - startTime
                
                // 更新统计信息
                inferenceCounter.incrementAndGet()
                totalInferenceTime.addAndGet(inferenceTime)
                
                Timber.d("推理完成，耗时: ${inferenceTime}ms, 输出长度: ${result.length}")
                
                result
                
            } finally {
                isInferencing.set(false)
            }
        }
    }
    
    /**
     * 批量推理（提高吞吐量）
     */
    suspend fun batchInference(prompts: List<String>, maxTokens: Int = MAX_TOKENS): List<String> {
        if (!isInitialized.get()) {
            throw IllegalStateException("LLM引擎未初始化")
        }
        
        return withContext(Dispatchers.Default) {
            prompts.map { prompt ->
                async {
                    // 简单的批量处理，可以优化为真正的批量推理
                    inference(prompt, maxTokens)
                }
            }.awaitAll()
        }
    }
    
    /**
     * 流式推理（实时输出）
     */
    suspend fun streamInference(
        prompt: String, 
        maxTokens: Int = MAX_TOKENS,
        onToken: (String) -> Unit
    ): String {
        if (!isInitialized.get()) {
            throw IllegalStateException("LLM引擎未初始化")
        }
        
        return withContext(Dispatchers.Default) {
            isInferencing.set(true)
            
            try {
                performStreamInference(modelHandle, prompt, maxTokens) { token ->
                    onToken(token)
                }
            } finally {
                isInferencing.set(false)
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        if (isInitialized.get()) {
            releaseNativeEngine(modelHandle)
            modelHandle = 0L
            isInitialized.set(false)
            
            // 取消所有推理任务
            inferenceScope.cancel()
            
            Timber.i("LLM引擎资源已释放")
        }
    }
    
    /**
     * 获取引擎状态
     */
    fun getStatus(): String {
        return if (isInitialized.get()) {
            val avgInferenceTime = if (inferenceCounter.get() > 0) {
                totalInferenceTime.get() / inferenceCounter.get()
            } else 0L
            
            "已初始化 - 模型: ${currentModelConfig?.modelName}, " +
            "推理次数: ${inferenceCounter.get()}, " +
            "平均耗时: ${avgInferenceTime}ms"
        } else {
            "未初始化"
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): LLMPerformanceStats {
        return LLMPerformanceStats(
            isInitialized = isInitialized.get(),
            isInferencing = isInferencing.get(),
            inferenceCount = inferenceCounter.get(),
            totalInferenceTime = totalInferenceTime.get(),
            averageInferenceTime = if (inferenceCounter.get() > 0) {
                totalInferenceTime.get() / inferenceCounter.get()
            } else 0L,
            modelConfig = currentModelConfig
        )
    }
    
    /**
     * 选择最优模型配置 - 内存优化版本
     */
    private fun selectOptimalModelConfig(): ModelConfig {
        val deviceRAM = getDeviceRAM()
        val availableRAM = getAvailableRAM()
        val hasNPU = hasNeuralProcessingUnit()
        val gpuCapability = getGPUCapability()
        val cpuCores = Runtime.getRuntime().availableProcessors()

        // 根据可用内存动态选择模型
        return when {
            // 高端设备 (8GB+) 且有足够可用内存
            deviceRAM >= 8 && availableRAM >= 3000 && hasNPU -> ModelConfig(
                modelName = "Phi-3-Mini-4K",
                modelFileName = "phi-3-mini-4k-q4_0.gguf", // ~2.3GB
                contextLength = 2048, // 减少上下文长度
                threads = cpuCores,
                useGPU = false,
                useNPU = true
            )
            // 中高端设备 (6GB+)
            deviceRAM >= 6 && availableRAM >= 2000 -> ModelConfig(
                modelName = "Qwen2-1.5B-Instruct",
                modelFileName = "qwen2-1_5b-instruct-q4_0.gguf", // ~900MB
                contextLength = 1024,
                threads = minOf(cpuCores, 4),
                useGPU = gpuCapability.isHigh,
                useNPU = false
            )
            // 中端设备 (4GB+)
            deviceRAM >= 4 && availableRAM >= 1000 -> ModelConfig(
                modelName = "TinyLlama-1.1B",
                modelFileName = "tinyllama-1.1b-chat-q4_0.gguf", // ~630MB
                contextLength = 512,
                threads = minOf(cpuCores, 3),
                useGPU = false,
                useNPU = false
            )
            // 低端设备或内存不足 - 使用混合模式
            else -> ModelConfig(
                modelName = "Hybrid-Mode", // 混合本地+云端
                modelFileName = "micro-llm-q8_0.gguf", // ~200MB 微型模型
                contextLength = 256,
                threads = minOf(cpuCores, 2),
                useGPU = false,
                useNPU = false
            )
        }
    }

    /**
     * 获取可用RAM (MB)
     */
    private fun getAvailableRAM(): Long {
        return try {
            val memInfo = android.app.ActivityManager.MemoryInfo()
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            activityManager.getMemoryInfo(memInfo)
            memInfo.availMem / (1024 * 1024) // 转换为MB
        } catch (e: Exception) {
            1000 // 默认1GB可用
        }
    }
    
    /**
     * 准备模型文件
     */
    private suspend fun prepareModelFile(modelFileName: String): String {
        val modelFile = File(context.filesDir, "models/$modelFileName")
        
        if (!modelFile.exists()) {
            Timber.i("模型文件不存在，开始从assets复制: $modelFileName")
            
            withContext(Dispatchers.IO) {
                copyModelFromAssets(modelFileName, modelFile)
            }
        }
        
        return modelFile.absolutePath
    }
    
    /**
     * 从assets复制模型文件
     */
    private fun copyModelFromAssets(modelFileName: String, targetFile: File) {
        targetFile.parentFile?.mkdirs()
        
        context.assets.open("$MODEL_DIR/$modelFileName").use { input ->
            FileOutputStream(targetFile).use { output ->
                input.copyTo(output)
            }
        }
        
        Timber.i("模型文件复制完成: ${targetFile.absolutePath}")
    }
    
    /**
     * 预热模型
     */
    private suspend fun warmupModel() {
        Timber.i("开始预热模型...")
        
        val warmupPrompt = "Hello, how are you?"
        val result = performInference(modelHandle, warmupPrompt, 10)
        
        Timber.i("模型预热完成，输出: $result")
    }
    
    /**
     * 获取设备RAM大小（GB）
     */
    private fun getDeviceRAM(): Int {
        return try {
            val memInfo = android.app.ActivityManager.MemoryInfo()
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            activityManager.getMemoryInfo(memInfo)
            (memInfo.totalMem / (1024 * 1024 * 1024)).toInt()
        } catch (e: Exception) {
            4 // 默认4GB
        }
    }
    
    /**
     * 检查是否有NPU
     */
    private fun hasNeuralProcessingUnit(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
               context.packageManager.hasSystemFeature("android.hardware.neuralnetworks")
    }
    
    /**
     * 获取GPU能力
     */
    private fun getGPUCapability(): GPUCapability {
        return try {
            val glRenderer = android.opengl.GLES20.glGetString(android.opengl.GLES20.GL_RENDERER)
            when {
                glRenderer.contains("Adreno 6") || glRenderer.contains("Mali-G7") -> GPUCapability.HIGH
                glRenderer.contains("Adreno 5") || glRenderer.contains("Mali-G5") -> GPUCapability.MEDIUM
                else -> GPUCapability.LOW
            }
        } catch (e: Exception) {
            GPUCapability.LOW
        }
    }
    
    // 原生方法声明
    private external fun initializeNativeEngine(modelPath: String, contextLength: Int, threads: Int): Long
    private external fun performInference(modelHandle: Long, prompt: String, maxTokens: Int): String
    private external fun performStreamInference(
        modelHandle: Long, 
        prompt: String, 
        maxTokens: Int, 
        onToken: (String) -> Unit
    ): String
    private external fun releaseNativeEngine(modelHandle: Long)
}

/**
 * 模型配置
 */
data class ModelConfig(
    val modelName: String,
    val modelFileName: String,
    val contextLength: Int,
    val threads: Int,
    val useGPU: Boolean = false,
    val useNPU: Boolean = false
)

/**
 * GPU能力等级
 */
enum class GPUCapability(val isHigh: Boolean) {
    HIGH(true),
    MEDIUM(false),
    LOW(false)
}

/**
 * LLM性能统计
 */
data class LLMPerformanceStats(
    val isInitialized: Boolean,
    val isInferencing: Boolean,
    val inferenceCount: Long,
    val totalInferenceTime: Long,
    val averageInferenceTime: Long,
    val modelConfig: ModelConfig?
)
