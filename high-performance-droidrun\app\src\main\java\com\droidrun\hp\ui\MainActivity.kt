package com.droidrun.hp.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.provider.Settings
import android.view.Menu
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.droidrun.hp.R
import com.droidrun.hp.databinding.ActivityMainBinding
import com.droidrun.hp.data.model.TaskInfo
import com.droidrun.hp.data.model.TaskStatus
import com.droidrun.hp.service.DroidRunCoreService
import com.droidrun.hp.service.HighPerformanceAccessibilityService
import com.droidrun.hp.ui.adapter.TaskListAdapter
import com.droidrun.hp.ui.viewmodel.MainViewModel
import com.droidrun.hp.utils.PermissionHelper
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*

/**
 * 主界面Activity
 * 显示任务列表、服务状态和快速操作
 */
class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()
    private lateinit var taskAdapter: TaskListAdapter
    
    // 广播接收器
    private val taskStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                "com.droidrun.hp.TASK_COMPLETE" -> {
                    val taskId = intent.getStringExtra("task_id")
                    val success = intent.getBooleanExtra("success", false)
                    handleTaskComplete(taskId, success)
                }
                "com.droidrun.hp.TASK_CANCELLED" -> {
                    val taskId = intent.getStringExtra("task_id")
                    handleTaskCancelled(taskId)
                }
                "com.droidrun.hp.TASK_ERROR" -> {
                    val taskId = intent.getStringExtra("task_id")
                    val error = intent.getStringExtra("error")
                    handleTaskError(taskId, error)
                }
                "com.droidrun.hp.SERVICE_STATUS" -> {
                    updateServiceStatus()
                }
                "com.droidrun.hp.ACCESSIBILITY_SERVICE_READY" -> {
                    updateAccessibilityServiceStatus()
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        super.onCreate(savedInstanceState)
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerView()
        setupFab()
        setupObservers()
        
        // 检查权限和服务状态
        checkPermissionsAndServices()
        
        // 注册广播接收器
        registerBroadcastReceivers()
        
        Timber.d("MainActivity创建完成")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(taskStatusReceiver)
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_performance -> {
                startActivity(Intent(this, PerformanceMonitorActivity::class.java))
                true
            }
            R.id.action_settings -> {
                startActivity(Intent(this, SettingsActivity::class.java))
                true
            }
            R.id.action_help -> {
                showHelpDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = getString(R.string.app_name)
    }
    
    private fun setupRecyclerView() {
        taskAdapter = TaskListAdapter(
            onTaskClick = { task -> showTaskDetails(task) },
            onTaskCancel = { task -> cancelTask(task) },
            onTaskRetry = { task -> retryTask(task) }
        )
        
        binding.recyclerViewTasks.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = taskAdapter
        }
    }
    
    private fun setupFab() {
        binding.fabNewTask.setOnClickListener {
            showNewTaskDialog()
        }
    }
    
    private fun setupObservers() {
        // 观察任务列表
        viewModel.tasks.observe(this) { tasks ->
            taskAdapter.submitList(tasks)
            updateEmptyState(tasks.isEmpty())
        }
        
        // 观察服务状态
        viewModel.serviceStatus.observe(this) { status ->
            updateServiceStatusUI(status)
        }
        
        // 观察无障碍服务状态
        viewModel.accessibilityServiceEnabled.observe(this) { enabled ->
            updateAccessibilityStatusUI(enabled)
        }
        
        // 观察错误消息
        viewModel.errorMessage.observe(this) { message ->
            if (message.isNotEmpty()) {
                showErrorSnackbar(message)
            }
        }
    }
    
    private fun checkPermissionsAndServices() {
        lifecycleScope.launch {
            // 检查基本权限
            if (!PermissionHelper.hasBasicPermissions(this@MainActivity)) {
                PermissionHelper.requestBasicPermissions(this@MainActivity)
            }
            
            // 检查无障碍服务
            if (!isAccessibilityServiceEnabled()) {
                showAccessibilityServiceDialog()
            }
            
            // 启动核心服务
            startCoreService()
            
            // 更新状态
            viewModel.refreshServiceStatus()
        }
    }
    
    private fun registerBroadcastReceivers() {
        val filter = IntentFilter().apply {
            addAction("com.droidrun.hp.TASK_COMPLETE")
            addAction("com.droidrun.hp.TASK_CANCELLED")
            addAction("com.droidrun.hp.TASK_ERROR")
            addAction("com.droidrun.hp.SERVICE_STATUS")
            addAction("com.droidrun.hp.ACCESSIBILITY_SERVICE_READY")
        }
        registerReceiver(taskStatusReceiver, filter)
    }
    
    private fun showNewTaskDialog() {
        val dialogBinding = DialogNewTaskBinding.inflate(layoutInflater)
        
        MaterialAlertDialogBuilder(this)
            .setTitle("新建任务")
            .setView(dialogBinding.root)
            .setPositiveButton("执行") { _, _ ->
                val command = dialogBinding.editTextCommand.text.toString().trim()
                if (command.isNotEmpty()) {
                    executeTask(command)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun executeTask(command: String) {
        lifecycleScope.launch {
            try {
                val taskId = UUID.randomUUID().toString()
                
                // 创建任务信息
                val taskInfo = TaskInfo(
                    id = taskId,
                    command = command,
                    status = TaskStatus.PENDING
                )
                
                // 添加到任务列表
                viewModel.addTask(taskInfo)
                
                // 启动任务执行
                val intent = Intent(this@MainActivity, DroidRunCoreService::class.java).apply {
                    action = DroidRunCoreService.ACTION_START_TASK
                    putExtra(DroidRunCoreService.EXTRA_TASK_ID, taskId)
                    putExtra(DroidRunCoreService.EXTRA_TASK_COMMAND, command)
                }
                startService(intent)
                
                showSuccessSnackbar("任务已开始执行")
                
            } catch (e: Exception) {
                Timber.e(e, "执行任务失败")
                showErrorSnackbar("执行任务失败: ${e.message}")
            }
        }
    }
    
    private fun cancelTask(task: TaskInfo) {
        lifecycleScope.launch {
            try {
                val intent = Intent(this@MainActivity, DroidRunCoreService::class.java).apply {
                    action = DroidRunCoreService.ACTION_STOP_TASK
                    putExtra(DroidRunCoreService.EXTRA_TASK_ID, task.id)
                }
                startService(intent)
                
                viewModel.updateTaskStatus(task.id, TaskStatus.CANCELLED)
                showSuccessSnackbar("任务已取消")
                
            } catch (e: Exception) {
                Timber.e(e, "取消任务失败")
                showErrorSnackbar("取消任务失败: ${e.message}")
            }
        }
    }
    
    private fun retryTask(task: TaskInfo) {
        executeTask(task.command)
    }
    
    private fun showTaskDetails(task: TaskInfo) {
        val intent = Intent(this, TaskDetailActivity::class.java).apply {
            putExtra("task_info", task)
        }
        startActivity(intent)
    }
    
    private fun startCoreService() {
        val intent = Intent(this, DroidRunCoreService::class.java)
        startForegroundService(intent)
    }
    
    private fun isAccessibilityServiceEnabled(): Boolean {
        val accessibilityEnabled = try {
            Settings.Secure.getInt(
                contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            0
        }
        
        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            return services?.contains(packageName) == true
        }
        
        return false
    }
    
    private fun showAccessibilityServiceDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("启用无障碍服务")
            .setMessage("DroidRun需要无障碍服务权限来控制设备。请在设置中启用DroidRun的无障碍服务。")
            .setPositiveButton("去设置") { _, _ ->
                val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                startActivity(intent)
            }
            .setNegativeButton("稍后", null)
            .setCancelable(false)
            .show()
    }
    
    private fun showHelpDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("帮助")
            .setMessage("""
                DroidRun高性能版使用说明：
                
                1. 确保已启用无障碍服务
                2. 点击右下角的+按钮创建新任务
                3. 输入自然语言命令，如"打开微信"
                4. 系统将自动执行相应操作
                
                支持的命令类型：
                • 应用操作：打开/关闭应用
                • 界面操作：点击、滑动、输入
                • 系统操作：返回、主页、设置
                
                更多帮助请访问官方文档。
            """.trimIndent())
            .setPositiveButton("确定", null)
            .show()
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        binding.textViewEmpty.visibility = if (isEmpty) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
    }
    
    private fun updateServiceStatusUI(status: String) {
        binding.textViewServiceStatus.text = "服务状态: $status"
    }
    
    private fun updateAccessibilityStatusUI(enabled: Boolean) {
        binding.textViewAccessibilityStatus.text = if (enabled) {
            "无障碍服务: 已启用"
        } else {
            "无障碍服务: 未启用"
        }
        
        binding.textViewAccessibilityStatus.setTextColor(
            if (enabled) {
                getColor(R.color.status_success)
            } else {
                getColor(R.color.status_error)
            }
        )
    }
    
    private fun handleTaskComplete(taskId: String?, success: Boolean) {
        taskId?.let {
            viewModel.updateTaskStatus(it, if (success) TaskStatus.COMPLETED else TaskStatus.FAILED)
            showSuccessSnackbar(if (success) "任务执行成功" else "任务执行失败")
        }
    }
    
    private fun handleTaskCancelled(taskId: String?) {
        taskId?.let {
            viewModel.updateTaskStatus(it, TaskStatus.CANCELLED)
            showSuccessSnackbar("任务已取消")
        }
    }
    
    private fun handleTaskError(taskId: String?, error: String?) {
        taskId?.let {
            viewModel.updateTaskStatus(it, TaskStatus.FAILED)
            showErrorSnackbar("任务执行错误: ${error ?: "未知错误"}")
        }
    }
    
    private fun updateServiceStatus() {
        viewModel.refreshServiceStatus()
    }
    
    private fun updateAccessibilityServiceStatus() {
        viewModel.refreshAccessibilityServiceStatus()
    }
    
    private fun showSuccessSnackbar(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
            .setBackgroundTint(getColor(R.color.status_success))
            .show()
    }
    
    private fun showErrorSnackbar(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG)
            .setBackgroundTint(getColor(R.color.status_error))
            .show()
    }
}
