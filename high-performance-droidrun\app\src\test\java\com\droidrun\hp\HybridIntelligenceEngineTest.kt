package com.droidrun.hp

import android.content.Context
import com.droidrun.hp.core.engine.HybridIntelligenceEngine
import com.droidrun.hp.data.model.*
import com.droidrun.hp.network.CloudRAGClient
import com.droidrun.hp.network.LLMApiClient
import io.mockk.*
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * 混合智能引擎测试
 */
class HybridIntelligenceEngineTest {
    
    private lateinit var context: Context
    private lateinit var llmApiClient: LLMApiClient
    private lateinit var cloudRAGClient: CloudRAGClient
    private lateinit var hybridEngine: HybridIntelligenceEngine
    
    @Before
    fun setup() {
        context = mockk(relaxed = true)
        llmApiClient = mockk(relaxed = true)
        cloudRAGClient = mockk(relaxed = true)
        
        hybridEngine = HybridIntelligenceEngine(context, llmApiClient, cloudRAGClient)
    }
    
    @Test
    fun `test local decision for simple click operation`() = runBlocking {
        // 准备测试数据
        val uiElement = UIElement(
            id = "button_1",
            className = "android.widget.Button",
            text = "登录",
            contentDescription = "",
            bounds = android.graphics.Rect(100, 100, 200, 150),
            isClickable = true,
            isScrollable = false,
            isEditable = false,
            isCheckable = false,
            isChecked = false,
            isEnabled = true,
            isVisible = true
        )
        
        val uiState = UIState(
            timestamp = System.currentTimeMillis(),
            elements = listOf(uiElement),
            rootNodeId = "root"
        )
        
        val request = DecisionRequest(
            goal = "点击登录按钮",
            currentUIState = uiState,
            history = emptyList()
        )
        
        // 初始化引擎
        val config = HybridEngineConfig(
            llmProvider = LLMProvider.OPENAI,
            ragProvider = RAGProvider.PINECONE,
            apiKey = "test_key",
            baseUrl = "https://api.test.com"
        )
        hybridEngine.initialize(config)
        
        // 执行决策
        val response = hybridEngine.makeDecision(request)
        
        // 验证结果
        assertEquals(DecisionStrategy.LOCAL, response.strategy)
        assertTrue(response.confidence > 0.8f)
        assertEquals(1, response.actions.size)
        assertEquals(UIAction.Type.CLICK, response.actions[0].type)
        assertEquals("button_1", response.actions[0].targetElementId)
    }
    
    @Test
    fun `test cloud decision for complex operation`() = runBlocking {
        // Mock RAG响应
        val ragResult = RAGResult(
            query = "复杂操作",
            results = listOf(
                RAGResult.RelevantEntry(
                    entry = KnowledgeEntry(
                        id = "kb_1",
                        title = "复杂操作指南",
                        content = "执行复杂操作的步骤...",
                        category = "android_automation"
                    ),
                    similarity = 0.9f,
                    relevanceScore = 0.9f
                )
            ),
            context = "相关知识上下文",
            processingTimeMs = 100
        )
        
        // Mock LLM响应
        val llmResponse = LLMResponse(
            id = "llm_1",
            content = """{"thought": "需要执行复杂操作", "action": "click", "target": "目标按钮", "parameters": {}}""",
            model = "gpt-4",
            usage = LLMUsage(10, 20, 30),
            finishReason = "stop"
        )
        
        coEvery { cloudRAGClient.queryRAG(any()) } returns ragResult
        coEvery { llmApiClient.callLLM(any()) } returns llmResponse
        
        val request = DecisionRequest(
            goal = "执行一个非常复杂的多步骤操作",
            currentUIState = null,
            history = emptyList()
        )
        
        // 初始化引擎
        val config = HybridEngineConfig(
            llmProvider = LLMProvider.OPENAI,
            ragProvider = RAGProvider.PINECONE,
            apiKey = "test_key",
            baseUrl = "https://api.test.com"
        )
        hybridEngine.initialize(config)
        
        // 执行决策
        val response = hybridEngine.makeDecision(request)
        
        // 验证结果
        assertEquals(DecisionStrategy.CLOUD, response.strategy)
        assertTrue(response.confidence > 0.7f)
        
        // 验证调用了外部服务
        coVerify { cloudRAGClient.queryRAG(any()) }
        coVerify { llmApiClient.callLLM(any()) }
    }
    
    @Test
    fun `test performance stats tracking`() = runBlocking {
        val config = HybridEngineConfig(
            llmProvider = LLMProvider.OPENAI,
            ragProvider = RAGProvider.PINECONE,
            apiKey = "test_key",
            baseUrl = "https://api.test.com"
        )
        hybridEngine.initialize(config)
        
        // 执行几次决策
        repeat(5) {
            val request = DecisionRequest(
                goal = "点击按钮$it",
                currentUIState = createMockUIState(),
                history = emptyList()
            )
            hybridEngine.makeDecision(request)
        }
        
        // 检查性能统计
        val stats = hybridEngine.getPerformanceStats()
        assertEquals(5, stats.totalRequests)
        assertTrue(stats.localDecisions > 0)
    }
    
    private fun createMockUIState(): UIState {
        return UIState(
            timestamp = System.currentTimeMillis(),
            elements = listOf(
                UIElement(
                    id = "test_button",
                    className = "android.widget.Button",
                    text = "测试按钮",
                    contentDescription = "",
                    bounds = android.graphics.Rect(0, 0, 100, 50),
                    isClickable = true,
                    isScrollable = false,
                    isEditable = false,
                    isCheckable = false,
                    isChecked = false,
                    isEnabled = true,
                    isVisible = true
                )
            ),
            rootNodeId = "root"
        )
    }
}

/**
 * 性能基准测试
 */
class PerformanceBenchmarkTest {
    
    @Test
    fun `benchmark local decision performance`() = runBlocking {
        val context = mockk<Context>(relaxed = true)
        val llmApiClient = mockk<LLMApiClient>(relaxed = true)
        val cloudRAGClient = mockk<CloudRAGClient>(relaxed = true)
        
        val hybridEngine = HybridIntelligenceEngine(context, llmApiClient, cloudRAGClient)
        
        val config = HybridEngineConfig(
            llmProvider = LLMProvider.OPENAI,
            ragProvider = RAGProvider.PINECONE,
            apiKey = "test_key",
            baseUrl = "https://api.test.com"
        )
        hybridEngine.initialize(config)
        
        val request = DecisionRequest(
            goal = "点击登录按钮",
            currentUIState = createSimpleUIState(),
            history = emptyList()
        )
        
        // 预热
        repeat(10) {
            hybridEngine.makeDecision(request)
        }
        
        // 基准测试
        val iterations = 100
        val startTime = System.currentTimeMillis()
        
        repeat(iterations) {
            hybridEngine.makeDecision(request)
        }
        
        val endTime = System.currentTimeMillis()
        val averageTime = (endTime - startTime) / iterations
        
        println("本地决策平均耗时: ${averageTime}ms")
        
        // 验证性能要求：本地决策应该在50ms内完成
        assertTrue("本地决策耗时过长: ${averageTime}ms", averageTime < 50)
    }
    
    private fun createSimpleUIState(): UIState {
        return UIState(
            timestamp = System.currentTimeMillis(),
            elements = listOf(
                UIElement(
                    id = "login_button",
                    className = "android.widget.Button",
                    text = "登录",
                    contentDescription = "",
                    bounds = android.graphics.Rect(100, 100, 200, 150),
                    isClickable = true,
                    isScrollable = false,
                    isEditable = false,
                    isCheckable = false,
                    isChecked = false,
                    isEnabled = true,
                    isVisible = true
                )
            ),
            rootNodeId = "root"
        )
    }
}

/**
 * 缓存性能测试
 */
class CachePerformanceTest {
    
    @Test
    fun `test decision cache effectiveness`() = runBlocking {
        val context = mockk<Context>(relaxed = true)
        val llmApiClient = mockk<LLMApiClient>(relaxed = true)
        val cloudRAGClient = mockk<CloudRAGClient>(relaxed = true)
        
        val hybridEngine = HybridIntelligenceEngine(context, llmApiClient, cloudRAGClient)
        
        val config = HybridEngineConfig(
            llmProvider = LLMProvider.OPENAI,
            ragProvider = RAGProvider.PINECONE,
            apiKey = "test_key",
            baseUrl = "https://api.test.com"
        )
        hybridEngine.initialize(config)
        
        val request = DecisionRequest(
            goal = "点击登录按钮",
            currentUIState = createSimpleUIState(),
            history = emptyList()
        )
        
        // 第一次请求（应该计算决策）
        val startTime1 = System.currentTimeMillis()
        val response1 = hybridEngine.makeDecision(request)
        val time1 = System.currentTimeMillis() - startTime1
        
        // 第二次相同请求（应该命中缓存）
        val startTime2 = System.currentTimeMillis()
        val response2 = hybridEngine.makeDecision(request)
        val time2 = System.currentTimeMillis() - startTime2
        
        // 验证缓存效果
        assertEquals(response1.strategy, response2.strategy)
        assertEquals(response1.actions.size, response2.actions.size)
        
        // 缓存命中应该显著更快
        assertTrue("缓存未生效，第二次请求耗时: ${time2}ms", time2 < time1 / 2)
        
        println("第一次请求耗时: ${time1}ms")
        println("第二次请求耗时: ${time2}ms (缓存命中)")
        println("性能提升: ${((time1 - time2).toFloat() / time1 * 100).toInt()}%")
    }
    
    private fun createSimpleUIState(): UIState {
        return UIState(
            timestamp = System.currentTimeMillis(),
            elements = listOf(
                UIElement(
                    id = "login_button",
                    className = "android.widget.Button", 
                    text = "登录",
                    contentDescription = "",
                    bounds = android.graphics.Rect(100, 100, 200, 150),
                    isClickable = true,
                    isScrollable = false,
                    isEditable = false,
                    isCheckable = false,
                    isChecked = false,
                    isEnabled = true,
                    isVisible = true
                )
            ),
            rootNodeId = "root"
        )
    }
}
