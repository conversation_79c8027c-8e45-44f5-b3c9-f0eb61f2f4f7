# Quick Fix for Java and Build Issues
Write-Host "DroidRun Quick Fix" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# Step 1: Fix gradle.properties
Write-Host "Fixing gradle.properties..." -ForegroundColor Yellow

$gradleProps = @"
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m
org.gradle.daemon=false
org.gradle.parallel=false
android.useAndroidX=true
android.enableJetifier=true
"@

$gradleProps | Out-File -FilePath "gradle.properties" -Encoding UTF8
Write-Host "gradle.properties fixed (removed problematic Java home)" -ForegroundColor Green

# Step 2: Set environment
Write-Host "Setting environment..." -ForegroundColor Yellow
$env:GRADLE_HOME = "C:\gradle\gradle-8.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"

# Step 3: Try build with system Java
Write-Host "Attempting build with current Java..." -ForegroundColor Yellow
$gradlePath = "$env:GRADLE_HOME\bin\gradle.bat"

try {
    Write-Host "Building..." -ForegroundColor Cyan
    & $gradlePath assembleRelease --no-daemon --stacktrace
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "BUILD SUCCESS!" -ForegroundColor Green
        if (Test-Path "app\build\outputs\apk\release\app-release.apk") {
            Write-Host "APK: app\build\outputs\apk\release\app-release.apk" -ForegroundColor Cyan
        }
    } else {
        Write-Host "Build failed. Java 11+ is required." -ForegroundColor Red
        Write-Host "Please run: .\fix-java-and-build.ps1" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "Press Enter to exit"
