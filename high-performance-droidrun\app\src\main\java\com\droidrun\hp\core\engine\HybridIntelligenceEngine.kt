package com.droidrun.hp.core.engine

import android.content.Context
import com.droidrun.hp.data.model.*
import com.droidrun.hp.network.CloudRAGClient
import com.droidrun.hp.network.LLMApiClient
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 混合智能引擎
 * 结合本地决策、云端LLM和RAG，提供智能的Android自动化
 */
@Singleton
class HybridIntelligenceEngine @Inject constructor(
    private val context: Context,
    private val llmApiClient: LLMApiClient,
    private val cloudRAGClient: CloudRAGClient
) {
    
    companion object {
        private const val LOCAL_DECISION_THRESHOLD = 0.8f
        private const val SIMPLE_OPERATION_PATTERNS = listOf(
            "点击", "滑动", "输入", "返回", "主页", "打开", "关闭"
        )
        private const val MAX_CONTEXT_LENGTH = 2048
        private const val CACHE_SIZE = 100
    }
    
    // 引擎状态
    private val isInitialized = AtomicBoolean(false)
    private val requestCounter = AtomicLong(0)
    private val localDecisionCounter = AtomicLong(0)
    private val cloudDecisionCounter = AtomicLong(0)
    
    // 本地决策缓存
    private val decisionCache = LRUCache<String, CachedDecision>(CACHE_SIZE)
    private val operationPatterns = mutableMapOf<String, OperationPattern>()
    
    // 配置
    private var llmProvider: LLMProvider = LLMProvider.OPENAI
    private var ragProvider: RAGProvider = RAGProvider.PINECONE
    private var apiKey: String = ""
    private var baseUrl: String = ""
    
    /**
     * 初始化混合智能引擎
     */
    suspend fun initialize(config: HybridEngineConfig) {
        if (isInitialized.get()) {
            Timber.w("混合智能引擎已经初始化")
            return
        }
        
        try {
            Timber.i("开始初始化混合智能引擎...")
            
            PerformanceProfiler.measureExecutionTimeAsync("HybridEngine.Initialize") {
                // 设置配置
                llmProvider = config.llmProvider
                ragProvider = config.ragProvider
                apiKey = config.apiKey
                baseUrl = config.baseUrl
                
                // 加载本地操作模式
                loadLocalOperationPatterns()
                
                // 预热缓存
                warmupCache()
                
                isInitialized.set(true)
                Timber.i("混合智能引擎初始化完成")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "混合智能引擎初始化失败")
            throw e
        }
    }
    
    /**
     * 智能决策：选择最佳的执行策略
     */
    suspend fun makeDecision(request: DecisionRequest): DecisionResponse {
        if (!isInitialized.get()) {
            throw IllegalStateException("混合智能引擎未初始化")
        }
        
        requestCounter.incrementAndGet()
        
        return PerformanceProfiler.measureExecutionTimeAsync("HybridEngine.MakeDecision") {
            // 1. 检查缓存
            val cacheKey = generateCacheKey(request)
            val cachedDecision = decisionCache.get(cacheKey)
            
            if (cachedDecision != null && !cachedDecision.isExpired()) {
                Timber.d("决策命中缓存: ${request.goal}")
                return@measureExecutionTimeAsync cachedDecision.response
            }
            
            // 2. 尝试本地决策
            val localDecision = tryLocalDecision(request)
            if (localDecision != null) {
                localDecisionCounter.incrementAndGet()
                Timber.d("使用本地决策: ${request.goal}")
                
                // 缓存本地决策
                decisionCache.put(cacheKey, CachedDecision(localDecision, System.currentTimeMillis() + 300000))
                return@measureExecutionTimeAsync localDecision
            }
            
            // 3. 使用云端智能决策
            val cloudDecision = makeCloudDecision(request)
            cloudDecisionCounter.incrementAndGet()
            Timber.d("使用云端决策: ${request.goal}")
            
            // 缓存云端决策
            decisionCache.put(cacheKey, CachedDecision(cloudDecision, System.currentTimeMillis() + 600000))
            
            cloudDecision
        }
    }
    
    /**
     * 尝试本地决策
     */
    private suspend fun tryLocalDecision(request: DecisionRequest): DecisionResponse? {
        // 检查是否是简单操作
        val isSimpleOperation = SIMPLE_OPERATION_PATTERNS.any { pattern ->
            request.goal.contains(pattern, ignoreCase = true)
        }
        
        if (!isSimpleOperation) {
            return null
        }
        
        // 基于UI状态的简单决策
        val uiState = request.currentUIState
        if (uiState == null || uiState.elements.isEmpty()) {
            return null
        }
        
        // 简单的模式匹配
        val action = when {
            request.goal.contains("点击", ignoreCase = true) -> {
                findClickableElement(request.goal, uiState)?.let { element ->
                    UIAction(
                        id = "local_${System.currentTimeMillis()}",
                        type = UIAction.Type.CLICK,
                        targetElementId = element.id,
                        coordinates = UIAction.Coordinates(
                            element.bounds.centerX(),
                            element.bounds.centerY()
                        )
                    )
                }
            }
            request.goal.contains("输入", ignoreCase = true) -> {
                findEditableElement(uiState)?.let { element ->
                    val inputText = extractInputText(request.goal)
                    UIAction(
                        id = "local_${System.currentTimeMillis()}",
                        type = UIAction.Type.INPUT,
                        targetElementId = element.id,
                        inputText = inputText
                    )
                }
            }
            request.goal.contains("返回", ignoreCase = true) -> {
                UIAction(
                    id = "local_${System.currentTimeMillis()}",
                    type = UIAction.Type.BACK,
                    targetElementId = ""
                )
            }
            request.goal.contains("主页", ignoreCase = true) -> {
                UIAction(
                    id = "local_${System.currentTimeMillis()}",
                    type = UIAction.Type.HOME,
                    targetElementId = ""
                )
            }
            else -> null
        }
        
        return if (action != null) {
            DecisionResponse(
                strategy = DecisionStrategy.LOCAL,
                confidence = 0.9f,
                actions = listOf(action),
                reasoning = "本地模式匹配决策",
                estimatedDuration = 500L
            )
        } else {
            null
        }
    }
    
    /**
     * 云端智能决策
     */
    private suspend fun makeCloudDecision(request: DecisionRequest): DecisionResponse {
        return withContext(Dispatchers.IO) {
            // 并行执行RAG查询和LLM推理
            val ragDeferred = async { queryRelevantKnowledge(request) }
            val llmDeferred = async { performLLMReasoning(request, ragDeferred.await()) }
            
            val llmResponse = llmDeferred.await()
            val actions = parseActionsFromLLMResponse(llmResponse, request.currentUIState)
            
            DecisionResponse(
                strategy = DecisionStrategy.CLOUD,
                confidence = 0.8f,
                actions = actions,
                reasoning = llmResponse.content,
                estimatedDuration = 2000L
            )
        }
    }
    
    /**
     * 查询相关知识
     */
    private suspend fun queryRelevantKnowledge(request: DecisionRequest): RAGResult {
        val ragQuery = RAGQuery(
            text = request.goal,
            category = "android_automation",
            maxResults = 3,
            similarityThreshold = 0.7f,
            provider = ragProvider,
            apiKey = apiKey,
            baseUrl = baseUrl,
            collection = "android_knowledge"
        )
        
        return try {
            cloudRAGClient.queryRAG(ragQuery)
        } catch (e: Exception) {
            Timber.w("RAG查询失败，使用默认知识: ${e.message}")
            // 返回默认知识
            RAGResult(
                query = request.goal,
                results = emptyList(),
                context = "基础Android操作知识",
                processingTimeMs = 0
            )
        }
    }
    
    /**
     * 执行LLM推理
     */
    private suspend fun performLLMReasoning(request: DecisionRequest, ragResult: RAGResult): LLMResponse {
        val prompt = buildLLMPrompt(request, ragResult)
        
        val llmRequest = LLMRequest(
            prompt = prompt,
            model = getOptimalModel(),
            maxTokens = 512,
            temperature = 0.2f,
            provider = llmProvider,
            apiKey = apiKey,
            baseUrl = baseUrl
        )
        
        return try {
            llmApiClient.callLLM(llmRequest)
        } catch (e: Exception) {
            Timber.w("LLM调用失败，使用默认响应: ${e.message}")
            // 返回默认响应
            LLMResponse(
                id = "fallback_${System.currentTimeMillis()}",
                content = "无法连接到AI服务，请检查网络连接",
                model = llmRequest.model,
                usage = LLMUsage(0, 0, 0),
                finishReason = "error"
            )
        }
    }
    
    /**
     * 构建LLM提示
     */
    private fun buildLLMPrompt(request: DecisionRequest, ragResult: RAGResult): String {
        val uiElements = request.currentUIState?.elements?.take(10)?.joinToString("\n") { element ->
            "- ${element.className}: ${element.text} (${if (element.isClickable) "可点击" else "不可点击"})"
        } ?: "无UI信息"
        
        return """
        目标: ${request.goal}
        
        相关知识:
        ${ragResult.context}
        
        当前界面元素:
        $uiElements
        
        历史操作:
        ${request.history.takeLast(3).joinToString("\n") { "- ${it.action}: ${it.result}" }}
        
        请分析当前情况并返回JSON格式的操作指令，包含以下字段：
        - thought: 分析思路
        - action: 操作类型 (click/input/swipe/back/home等)
        - target: 目标元素描述
        - parameters: 操作参数
        
        示例：
        {
          "thought": "需要点击登录按钮",
          "action": "click",
          "target": "登录按钮",
          "parameters": {"text": "登录"}
        }
        """.trimIndent()
    }
    
    /**
     * 获取最优模型
     */
    private fun getOptimalModel(): String {
        return when (llmProvider) {
            LLMProvider.OPENAI -> "gpt-4o-mini"
            LLMProvider.ANTHROPIC -> "claude-3-haiku-20240307"
            LLMProvider.GEMINI -> "gemini-1.5-flash"
            LLMProvider.OLLAMA -> "llama3.1:8b"
            LLMProvider.CUSTOM -> "custom-model"
        }
    }
    
    /**
     * 解析LLM响应中的操作
     */
    private fun parseActionsFromLLMResponse(response: LLMResponse, uiState: UIState?): List<UIAction> {
        // 简化的解析逻辑，实际应该更复杂
        return try {
            val actionData = kotlinx.serialization.json.Json.decodeFromString<Map<String, Any>>(response.content)
            val action = actionData["action"] as? String ?: "no_action"
            val target = actionData["target"] as? String ?: ""
            
            when (action) {
                "click" -> {
                    val element = findElementByDescription(target, uiState)
                    if (element != null) {
                        listOf(UIAction(
                            id = "cloud_${System.currentTimeMillis()}",
                            type = UIAction.Type.CLICK,
                            targetElementId = element.id,
                            coordinates = UIAction.Coordinates(
                                element.bounds.centerX(),
                                element.bounds.centerY()
                            )
                        ))
                    } else {
                        emptyList()
                    }
                }
                "input" -> {
                    val inputText = (actionData["parameters"] as? Map<*, *>)?.get("text") as? String ?: ""
                    val element = findEditableElement(uiState)
                    if (element != null) {
                        listOf(UIAction(
                            id = "cloud_${System.currentTimeMillis()}",
                            type = UIAction.Type.INPUT,
                            targetElementId = element.id,
                            inputText = inputText
                        ))
                    } else {
                        emptyList()
                    }
                }
                else -> emptyList()
            }
        } catch (e: Exception) {
            Timber.e(e, "解析LLM响应失败")
            emptyList()
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): HybridEngineStats {
        return HybridEngineStats(
            isInitialized = isInitialized.get(),
            totalRequests = requestCounter.get(),
            localDecisions = localDecisionCounter.get(),
            cloudDecisions = cloudDecisionCounter.get(),
            cacheHitRate = if (requestCounter.get() > 0) {
                (localDecisionCounter.get().toFloat() / requestCounter.get()) * 100
            } else 0f,
            averageResponseTime = 0L // 需要实际计算
        )
    }
    
    // 辅助方法
    private fun loadLocalOperationPatterns() {
        // 加载本地操作模式
    }
    
    private fun warmupCache() {
        // 预热缓存
    }
    
    private fun generateCacheKey(request: DecisionRequest): String {
        return "${request.goal}_${request.currentUIState?.elements?.size}".hashCode().toString()
    }
    
    private fun findClickableElement(goal: String, uiState: UIState): UIElement? {
        return uiState.elements.find { element ->
            element.isClickable && (
                element.text.contains(extractTargetText(goal), ignoreCase = true) ||
                element.contentDescription.contains(extractTargetText(goal), ignoreCase = true)
            )
        }
    }
    
    private fun findEditableElement(uiState: UIState?): UIElement? {
        return uiState?.elements?.find { it.isEditable }
    }
    
    private fun findElementByDescription(description: String, uiState: UIState?): UIElement? {
        return uiState?.elements?.find { element ->
            element.text.contains(description, ignoreCase = true) ||
            element.contentDescription.contains(description, ignoreCase = true)
        }
    }
    
    private fun extractTargetText(goal: String): String {
        // 简单的文本提取逻辑
        return goal.substringAfter("点击").trim()
    }
    
    private fun extractInputText(goal: String): String {
        // 简单的输入文本提取逻辑
        return goal.substringAfter("输入").trim()
    }
    
    /**
     * 缓存的决策
     */
    private data class CachedDecision(
        val response: DecisionResponse,
        val expiryTime: Long
    ) {
        fun isExpired(): Boolean = System.currentTimeMillis() > expiryTime
    }
    
    /**
     * 操作模式
     */
    private data class OperationPattern(
        val pattern: String,
        val confidence: Float,
        val actionType: UIAction.Type
    )
}
