package com.droidrun.hp.data.repository

import com.droidrun.hp.data.dao.TaskDao
import com.droidrun.hp.data.dao.ActionHistoryDao
import com.droidrun.hp.data.entity.TaskEntity
import com.droidrun.hp.data.entity.ActionHistoryEntity
import com.droidrun.hp.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务数据仓库
 * 管理任务的CRUD操作和状态跟踪
 */
@Singleton
class TaskRepository @Inject constructor(
    private val taskDao: TaskDao,
    private val actionHistoryDao: ActionHistoryDao
) {
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    /**
     * 获取所有任务
     */
    fun getAllTasks(): Flow<List<TaskInfo>> {
        return taskDao.getAllTasks().map { entities ->
            entities.map { entity -> entity.toTaskInfo() }
        }
    }
    
    /**
     * 获取运行中的任务
     */
    fun getRunningTasks(): Flow<List<TaskInfo>> {
        return taskDao.getTasksByStatus(TaskStatus.RUNNING).map { entities ->
            entities.map { entity -> entity.toTaskInfo() }
        }
    }
    
    /**
     * 根据ID获取任务
     */
    suspend fun getTaskById(taskId: String): TaskInfo? {
        return taskDao.getTaskById(taskId)?.toTaskInfo()
    }
    
    /**
     * 创建新任务
     */
    suspend fun createTask(taskInfo: TaskInfo): Boolean {
        return try {
            val entity = taskInfo.toTaskEntity()
            taskDao.insertTask(entity)
            Timber.d("创建任务: ${taskInfo.id}")
            true
        } catch (e: Exception) {
            Timber.e(e, "创建任务失败: ${taskInfo.id}")
            false
        }
    }
    
    /**
     * 更新任务状态
     */
    suspend fun updateTaskStatus(taskId: String, status: TaskStatus): Boolean {
        return try {
            val currentTime = System.currentTimeMillis()
            when (status) {
                TaskStatus.RUNNING -> {
                    taskDao.updateTaskStatus(taskId, status, currentTime, null)
                }
                TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT -> {
                    taskDao.updateTaskStatus(taskId, status, null, currentTime)
                }
                else -> {
                    taskDao.updateTaskStatus(taskId, status, null, null)
                }
            }
            Timber.d("更新任务状态: $taskId -> $status")
            true
        } catch (e: Exception) {
            Timber.e(e, "更新任务状态失败: $taskId")
            false
        }
    }
    
    /**
     * 更新任务进度
     */
    suspend fun updateTaskProgress(taskId: String, progress: Float, currentStep: String): Boolean {
        return try {
            taskDao.updateTaskProgress(taskId, progress, currentStep)
            Timber.d("更新任务进度: $taskId -> $progress%")
            true
        } catch (e: Exception) {
            Timber.e(e, "更新任务进度失败: $taskId")
            false
        }
    }
    
    /**
     * 保存任务结果
     */
    suspend fun saveTaskResult(taskId: String, result: TaskResult): Boolean {
        return try {
            val executedActionsJson = result.executedActions.map { action ->
                json.encodeToString(action)
            }
            
            taskDao.updateTaskResult(
                taskId = taskId,
                success = result.success,
                errorMessage = if (!result.success) result.message else null,
                executedActions = executedActionsJson,
                completedAt = result.endTime
            )
            
            Timber.d("保存任务结果: $taskId -> ${if (result.success) "成功" else "失败"}")
            true
        } catch (e: Exception) {
            Timber.e(e, "保存任务结果失败: $taskId")
            false
        }
    }
    
    /**
     * 记录操作历史
     */
    suspend fun recordActionHistory(
        taskId: String,
        action: UIAction,
        success: Boolean,
        executionTime: Long,
        errorMessage: String? = null,
        uiStateBefore: UIState? = null,
        uiStateAfter: UIState? = null
    ): Boolean {
        return try {
            val entity = ActionHistoryEntity(
                id = "action_${System.currentTimeMillis()}_${(0..999).random()}",
                taskId = taskId,
                actionType = action.type,
                targetElementId = action.targetElementId,
                coordinates = action.coordinates?.let { json.encodeToString(it) },
                inputText = action.inputText,
                success = success,
                executionTime = executionTime,
                errorMessage = errorMessage,
                uiStateBefore = uiStateBefore?.let { json.encodeToString(it) },
                uiStateAfter = uiStateAfter?.let { json.encodeToString(it) }
            )
            
            actionHistoryDao.insertActionHistory(entity)
            Timber.d("记录操作历史: $taskId -> ${action.type}")
            true
        } catch (e: Exception) {
            Timber.e(e, "记录操作历史失败: $taskId")
            false
        }
    }
    
    /**
     * 获取任务的操作历史
     */
    suspend fun getTaskActionHistory(taskId: String): List<ActionHistoryEntity> {
        return try {
            actionHistoryDao.getActionHistoryByTaskId(taskId)
        } catch (e: Exception) {
            Timber.e(e, "获取操作历史失败: $taskId")
            emptyList()
        }
    }
    
    /**
     * 删除任务
     */
    suspend fun deleteTask(taskId: String): Boolean {
        return try {
            taskDao.deleteTask(taskId)
            Timber.d("删除任务: $taskId")
            true
        } catch (e: Exception) {
            Timber.e(e, "删除任务失败: $taskId")
            false
        }
    }
    
    /**
     * 清理旧任务
     */
    suspend fun cleanupOldTasks(daysToKeep: Int = 30): Int {
        return try {
            val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
            val deletedCount = taskDao.deleteTasksOlderThan(cutoffTime)
            Timber.i("清理旧任务: 删除了 $deletedCount 个任务")
            deletedCount
        } catch (e: Exception) {
            Timber.e(e, "清理旧任务失败")
            0
        }
    }
    
    /**
     * 获取任务统计信息
     */
    suspend fun getTaskStatistics(): TaskStatistics {
        return try {
            val totalTasks = taskDao.getTotalTaskCount()
            val completedTasks = taskDao.getTaskCountByStatus(TaskStatus.COMPLETED)
            val failedTasks = taskDao.getTaskCountByStatus(TaskStatus.FAILED)
            val runningTasks = taskDao.getTaskCountByStatus(TaskStatus.RUNNING)
            
            val successRate = if (totalTasks > 0) {
                (completedTasks.toFloat() / totalTasks) * 100
            } else 0f
            
            val averageExecutionTime = taskDao.getAverageExecutionTime()
            
            TaskStatistics(
                totalTasks = totalTasks,
                completedTasks = completedTasks,
                failedTasks = failedTasks,
                runningTasks = runningTasks,
                successRate = successRate,
                averageExecutionTime = averageExecutionTime
            )
        } catch (e: Exception) {
            Timber.e(e, "获取任务统计失败")
            TaskStatistics()
        }
    }
    
    /**
     * 搜索任务
     */
    suspend fun searchTasks(query: String): List<TaskInfo> {
        return try {
            val entities = taskDao.searchTasks("%$query%")
            entities.map { it.toTaskInfo() }
        } catch (e: Exception) {
            Timber.e(e, "搜索任务失败: $query")
            emptyList()
        }
    }
    
    /**
     * 获取最近的任务
     */
    suspend fun getRecentTasks(limit: Int = 10): List<TaskInfo> {
        return try {
            val entities = taskDao.getRecentTasks(limit)
            entities.map { it.toTaskInfo() }
        } catch (e: Exception) {
            Timber.e(e, "获取最近任务失败")
            emptyList()
        }
    }
    
    /**
     * 扩展函数：TaskEntity转TaskInfo
     */
    private fun TaskEntity.toTaskInfo(): TaskInfo {
        return TaskInfo(
            id = this.id,
            command = this.command,
            status = this.status,
            progress = this.progress,
            currentStep = this.currentStep,
            createdAt = this.createdAt,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            result = if (this.completedAt != null) {
                TaskResult(
                    taskId = this.id,
                    command = this.command,
                    success = this.success,
                    message = this.errorMessage ?: "任务完成",
                    startTime = this.startedAt ?: this.createdAt,
                    endTime = this.completedAt!!,
                    executedActions = this.executedActions.mapNotNull { actionJson ->
                        try {
                            json.decodeFromString<UIAction>(actionJson)
                        } catch (e: Exception) {
                            null
                        }
                    },
                    errorDetails = this.errorMessage
                )
            } else null
        )
    }
    
    /**
     * 扩展函数：TaskInfo转TaskEntity
     */
    private fun TaskInfo.toTaskEntity(): TaskEntity {
        return TaskEntity(
            id = this.id,
            command = this.command,
            status = this.status,
            progress = this.progress,
            currentStep = this.currentStep,
            createdAt = this.createdAt,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            success = this.result?.success ?: false,
            errorMessage = this.result?.errorDetails,
            executedActions = this.result?.executedActions?.map { action ->
                json.encodeToString(action)
            } ?: emptyList()
        )
    }
}

/**
 * 任务统计信息
 */
data class TaskStatistics(
    val totalTasks: Int = 0,
    val completedTasks: Int = 0,
    val failedTasks: Int = 0,
    val runningTasks: Int = 0,
    val successRate: Float = 0f,
    val averageExecutionTime: Long = 0L
)
