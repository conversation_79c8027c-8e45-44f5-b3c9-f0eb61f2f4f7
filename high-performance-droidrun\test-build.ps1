# Test Build with Fixed Configuration
Write-Host "Testing DroidRun Build with Fixed Configuration" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Set environment
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
$env:GRADLE_HOME = "C:\gradle\gradle-8.4"
$env:PATH = "$env:JAVA_HOME\bin;$env:GRADLE_HOME\bin;$env:PATH"

Write-Host "Environment:" -ForegroundColor Yellow
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan
Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan

Write-Host ""
Write-Host "Java version:" -ForegroundColor Yellow
& "$env:JAVA_HOME\bin\java.exe" -version

Write-Host ""
Write-Host "Gradle version:" -ForegroundColor Yellow
& "$env:GRADLE_HOME\bin\gradle.bat" --version | Select-String "Gradle"

Write-Host ""
Write-Host "Starting test build..." -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow

$gradlePath = "$env:GRADLE_HOME\bin\gradle.bat"

try {
    Write-Host "Step 1: Cleaning project..." -ForegroundColor Cyan
    & $gradlePath clean --no-daemon
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Clean successful" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Step 2: Building debug APK..." -ForegroundColor Cyan
        & $gradlePath assembleDebug --no-daemon --info
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "🎉 BUILD SUCCESS!" -ForegroundColor Green
            Write-Host "=================" -ForegroundColor Green
            
            # Check for APK
            $debugApk = "app\build\outputs\apk\debug\app-debug.apk"
            if (Test-Path $debugApk) {
                $apkFile = Get-Item $debugApk
                $sizeMB = [math]::Round($apkFile.Length / 1MB, 2)
                
                Write-Host ""
                Write-Host "📱 Debug APK created!" -ForegroundColor Green
                Write-Host "📍 Location: $debugApk" -ForegroundColor Cyan
                Write-Host "📦 Size: $($apkFile.Length) bytes (~$sizeMB MB)" -ForegroundColor Cyan
                Write-Host ""
                Write-Host "🔧 Install command:" -ForegroundColor Yellow
                Write-Host "   adb install `"$debugApk`"" -ForegroundColor White
                Write-Host ""
                Write-Host "Now trying release build..." -ForegroundColor Yellow
                
                # Try release build
                & $gradlePath assembleRelease --no-daemon
                
                if ($LASTEXITCODE -eq 0) {
                    $releaseApk = "app\build\outputs\apk\release\app-release.apk"
                    if (Test-Path $releaseApk) {
                        $releaseFile = Get-Item $releaseApk
                        $releaseSizeMB = [math]::Round($releaseFile.Length / 1MB, 2)
                        
                        Write-Host ""
                        Write-Host "🎉 RELEASE BUILD ALSO SUCCESS!" -ForegroundColor Green
                        Write-Host "📱 Release APK: $releaseApk" -ForegroundColor Cyan
                        Write-Host "📦 Size: $($releaseFile.Length) bytes (~$releaseSizeMB MB)" -ForegroundColor Cyan
                    }
                } else {
                    Write-Host "⚠️ Release build failed, but debug build works!" -ForegroundColor Yellow
                }
                
            } else {
                Write-Host "⚠️ APK not found in expected location" -ForegroundColor Yellow
                Get-ChildItem -Recurse -Filter "*.apk" | ForEach-Object {
                    Write-Host "Found: $($_.FullName)" -ForegroundColor Cyan
                }
            }
            
        } else {
            Write-Host ""
            Write-Host "❌ Build failed" -ForegroundColor Red
            Write-Host "Check the error messages above" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "❌ Clean failed" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Build failed with exception: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
