package com.droidrun.hp.network

import com.droidrun.hp.data.model.*
import com.droidrun.hp.utils.PerformanceProfiler
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 豆包服务客户端
 * 集成DeepSeek LLM、豆包Embedding和VikingDB向量数据库
 */
@Singleton
class DoubaoServiceClient @Inject constructor() {
    
    companion object {
        private const val TIMEOUT_SECONDS = 30L
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 1000L
        
        // 实际API地址
        private const val DEEPSEEK_API_URL = "https://api.deepseek.com/v1"
        private const val ARK_EMBEDDING_API_URL = "https://ark.cn-beijing.volces.com/api/v3/embeddings"
        private const val VIKING_DB_HOST = "api-vikingdb.volces.com"
        private const val CLOUD_KB_DOMAIN = "api-knowledgebase.mlp.cn-beijing.volces.com"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .addInterceptor(LoggingInterceptor())
        .build()
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    // API密钥配置
    private var deepseekApiKey: String = ""
    private var arkApiKey: String = ""
    private var vikingDbAk: String = ""
    private var vikingDbSk: String = ""
    
    /**
     * 配置API密钥
     */
    fun configure(
        deepseekKey: String,
        arkKey: String,
        vikingAk: String,
        vikingSk: String
    ) {
        deepseekApiKey = deepseekKey
        arkApiKey = arkKey
        vikingDbAk = vikingAk
        vikingDbSk = vikingSk
        Timber.i("豆包服务客户端配置完成")
    }
    
    /**
     * 调用DeepSeek LLM
     */
    suspend fun callDeepSeekLLM(
        prompt: String,
        model: String = "deepseek-chat",
        maxTokens: Int = 512,
        temperature: Float = 0.2f,
        systemPrompt: String? = null
    ): LLMResponse {
        return PerformanceProfiler.measureExecutionTimeAsync("DoubaoService.DeepSeekLLM") {
            val messages = mutableListOf<DeepSeekMessage>()
            
            systemPrompt?.let { messages.add(DeepSeekMessage("system", it)) }
            messages.add(DeepSeekMessage("user", prompt))
            
            val requestBody = json.encodeToString(
                DeepSeekRequest.serializer(),
                DeepSeekRequest(
                    model = model,
                    messages = messages,
                    temperature = temperature,
                    max_tokens = maxTokens
                )
            )
            
            val request = Request.Builder()
                .url("$DEEPSEEK_API_URL/chat/completions")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .addHeader("Authorization", "Bearer $deepseekApiKey")
                .addHeader("Content-Type", "application/json")
                .build()
            
            executeRequest(request) { responseBody ->
                val response = json.decodeFromString<DeepSeekResponse>(responseBody)
                LLMResponse(
                    id = response.id,
                    content = response.choices.firstOrNull()?.message?.content ?: "",
                    model = response.model,
                    usage = LLMUsage(
                        promptTokens = response.usage.prompt_tokens,
                        completionTokens = response.usage.completion_tokens,
                        totalTokens = response.usage.total_tokens
                    ),
                    finishReason = response.choices.firstOrNull()?.finish_reason ?: "unknown"
                )
            }
        }
    }
    
    /**
     * 生成文本嵌入向量
     */
    suspend fun generateEmbedding(
        texts: List<String>,
        model: String = "doubao-embedding-text-240715"
    ): List<List<Float>> {
        return PerformanceProfiler.measureExecutionTimeAsync("DoubaoService.Embedding") {
            val requestBody = json.encodeToString(
                DoubaoEmbeddingRequest.serializer(),
                DoubaoEmbeddingRequest(
                    model = model,
                    input = texts
                )
            )
            
            val request = Request.Builder()
                .url(ARK_EMBEDDING_API_URL)
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .addHeader("Authorization", "Bearer $arkApiKey")
                .addHeader("Content-Type", "application/json")
                .build()
            
            executeRequest(request) { responseBody ->
                val response = json.decodeFromString<DoubaoEmbeddingResponse>(responseBody)
                response.data.map { it.embedding }
            }
        }
    }
    
    /**
     * VikingDB向量搜索
     */
    suspend fun searchVikingDB(
        query: String,
        collection: String = "droidRunRag",
        topK: Int = 5
    ): RAGResult {
        return PerformanceProfiler.measureExecutionTimeAsync("DoubaoService.VikingDBSearch") {
            // 1. 先生成查询向量
            val queryEmbedding = generateEmbedding(listOf(query)).first()
            
            // 2. 执行向量搜索
            val searchRequest = VikingDBSearchRequest(
                collection = collection,
                vector = queryEmbedding,
                limit = topK,
                output_fields = listOf("content", "title", "category")
            )
            
            val requestBody = json.encodeToString(
                VikingDBSearchRequest.serializer(),
                searchRequest
            )
            
            val request = Request.Builder()
                .url("https://$VIKING_DB_HOST/api/index/search")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .addHeader("Authorization", buildVikingDBAuth())
                .addHeader("Content-Type", "application/json")
                .build()
            
            executeRequest(request) { responseBody ->
                val response = json.decodeFromString<VikingDBSearchResponse>(responseBody)
                
                val results = response.data.map { result ->
                    RAGResult.RelevantEntry(
                        entry = KnowledgeEntry(
                            id = result.id,
                            title = result.fields["title"] as? String ?: "",
                            content = result.fields["content"] as? String ?: "",
                            category = result.fields["category"] as? String ?: ""
                        ),
                        similarity = result.score,
                        relevanceScore = result.score
                    )
                }
                
                RAGResult(
                    query = query,
                    results = results,
                    context = results.take(3).joinToString("\n\n") { 
                        "${it.entry.title}: ${it.entry.content}" 
                    },
                    processingTimeMs = 0
                )
            }
        }
    }
    
    /**
     * 云知识库查询
     */
    suspend fun queryCloudKB(
        query: String,
        collectionName: String = "droidRunRag",
        topK: Int = 5,
        scoreThreshold: Float = 0.7f
    ): RAGResult {
        return PerformanceProfiler.measureExecutionTimeAsync("DoubaoService.CloudKB") {
            val requestBody = json.encodeToString(
                CloudKBQueryRequest.serializer(),
                CloudKBQueryRequest(
                    query = query,
                    collection_name = collectionName,
                    top_k = topK,
                    score_threshold = scoreThreshold
                )
            )
            
            val request = Request.Builder()
                .url("https://$CLOUD_KB_DOMAIN/api/knowledge_base/query")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .addHeader("Authorization", buildVikingDBAuth())
                .addHeader("Content-Type", "application/json")
                .build()
            
            executeRequest(request) { responseBody ->
                val response = json.decodeFromString<CloudKBQueryResponse>(responseBody)
                
                val results = response.data.chunks.map { chunk ->
                    RAGResult.RelevantEntry(
                        entry = KnowledgeEntry(
                            id = chunk.chunk_id,
                            title = chunk.metadata["title"] as? String ?: "",
                            content = chunk.content,
                            category = chunk.metadata["category"] as? String ?: ""
                        ),
                        similarity = chunk.score,
                        relevanceScore = chunk.score
                    )
                }
                
                RAGResult(
                    query = query,
                    results = results,
                    context = results.take(3).joinToString("\n\n") { 
                        "${it.entry.title}: ${it.entry.content}" 
                    },
                    processingTimeMs = 0
                )
            }
        }
    }
    
    /**
     * 执行HTTP请求
     */
    private suspend fun <T> executeRequest(
        request: Request,
        parser: (String) -> T
    ): T {
        return withContext(Dispatchers.IO) {
            var lastException: Exception? = null
            
            repeat(MAX_RETRIES) { attempt ->
                try {
                    val response = httpClient.newCall(request).execute()
                    
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string() ?: ""
                        return@withContext parser(responseBody)
                    } else {
                        throw ApiException("HTTP ${response.code}: ${response.message}")
                    }
                    
                } catch (e: Exception) {
                    lastException = e
                    Timber.w("豆包服务调用失败 (尝试 ${attempt + 1}/$MAX_RETRIES): ${e.message}")
                    
                    if (attempt < MAX_RETRIES - 1) {
                        delay(RETRY_DELAY_MS * (attempt + 1))
                    }
                }
            }
            
            throw lastException ?: Exception("豆包服务调用失败")
        }
    }
    
    /**
     * 构建VikingDB认证头
     */
    private fun buildVikingDBAuth(): String {
        // 简化实现，实际应该使用正确的签名算法
        return "Bearer $vikingDbAk:$vikingDbSk"
    }
    
    /**
     * 测试连接
     */
    suspend fun testConnection(): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()
        
        // 测试DeepSeek LLM
        try {
            callDeepSeekLLM("测试", maxTokens = 10)
            results["deepseek_llm"] = true
        } catch (e: Exception) {
            results["deepseek_llm"] = false
            Timber.e(e, "DeepSeek LLM连接测试失败")
        }
        
        // 测试豆包Embedding
        try {
            generateEmbedding(listOf("测试"))
            results["doubao_embedding"] = true
        } catch (e: Exception) {
            results["doubao_embedding"] = false
            Timber.e(e, "豆包Embedding连接测试失败")
        }
        
        // 测试VikingDB
        try {
            searchVikingDB("测试", topK = 1)
            results["viking_db"] = true
        } catch (e: Exception) {
            results["viking_db"] = false
            Timber.e(e, "VikingDB连接测试失败")
        }
        
        return results
    }
    
    /**
     * 日志拦截器
     */
    private class LoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val startTime = System.currentTimeMillis()
            
            val response = chain.proceed(request)
            
            val duration = System.currentTimeMillis() - startTime
            Timber.d("豆包服务请求: ${request.url} - ${response.code} (${duration}ms)")
            
            return response
        }
    }
    
    /**
     * API异常
     */
    class ApiException(message: String) : Exception(message)
}
