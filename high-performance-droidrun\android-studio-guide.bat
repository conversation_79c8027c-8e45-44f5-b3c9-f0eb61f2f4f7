@echo off
echo Android Studio Build Guide
echo ==========================
echo.
echo If Gradle wrapper issues persist, use Android Studio:
echo.
echo Step 1: Download Android Studio
echo   https://developer.android.com/studio
echo.
echo Step 2: Install Android Studio
echo   - Follow the installation wizard
echo   - Install Android SDK when prompted
echo.
echo Step 3: Open Project
echo   - Launch Android Studio
echo   - Click "Open an existing project"
echo   - Navigate to: %CD%
echo   - Select this folder and click OK
echo.
echo Step 4: Wait for Sync
echo   - Android Studio will sync the project
echo   - Download required dependencies
echo   - This may take several minutes
echo.
echo Step 5: Build APK
echo   - Click Build menu
echo   - Select "Build Bundle(s) / APK(s)"
echo   - Click "Build APK(s)"
echo   - Wait for build to complete
echo.
echo Step 6: Find APK
echo   - APK will be in: app\build\outputs\apk\release\
echo   - Click "locate" link in build notification
echo.
echo Alternative: Use Android Studio Terminal
echo   - Open Terminal tab in Android Studio
echo   - Run: .\gradlew assembleRelease
echo.
echo This method is most reliable for first-time builds!
echo.
pause
