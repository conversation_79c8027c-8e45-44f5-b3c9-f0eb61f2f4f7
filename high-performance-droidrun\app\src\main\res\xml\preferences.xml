<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- LLM配置分类 -->
    <PreferenceCategory
        android:title="LLM配置"
        android:summary="大语言模型相关设置">

        <ListPreference
            android:key="llm_provider"
            android:title="LLM提供商"
            android:summary="选择要使用的大语言模型提供商"
            android:defaultValue="openai"
            android:entries="@array/llm_providers"
            android:entryValues="@array/llm_provider_values" />

        <EditTextPreference
            android:key="llm_api_key"
            android:title="API密钥"
            android:summary="输入LLM服务的API密钥"
            android:inputType="textPassword"
            android:selectAllOnFocus="true" />

        <EditTextPreference
            android:key="llm_base_url"
            android:title="基础URL"
            android:summary="自定义API基础URL（可选）"
            android:inputType="textUri"
            android:selectAllOnFocus="true" />

        <ListPreference
            android:key="llm_model"
            android:title="模型选择"
            android:summary="选择要使用的具体模型"
            android:defaultValue="gpt-4o-mini" />

        <SeekBarPreference
            android:key="llm_temperature"
            android:title="温度设置"
            android:summary="控制输出的随机性 (0-100)"
            android:defaultValue="20"
            android:max="100"
            app:showSeekBarValue="true" />

        <EditTextPreference
            android:key="llm_max_tokens"
            android:title="最大Token数"
            android:summary="单次请求的最大Token数量"
            android:defaultValue="512"
            android:inputType="number" />

    </PreferenceCategory>

    <!-- RAG配置分类 -->
    <PreferenceCategory
        android:title="RAG配置"
        android:summary="检索增强生成相关设置">

        <SwitchPreferenceCompat
            android:key="enable_rag"
            android:title="启用RAG"
            android:summary="开启检索增强生成功能"
            android:defaultValue="true" />

        <ListPreference
            android:key="rag_provider"
            android:title="RAG提供商"
            android:summary="选择向量数据库提供商"
            android:defaultValue="pinecone"
            android:entries="@array/rag_providers"
            android:entryValues="@array/rag_provider_values"
            android:dependency="enable_rag" />

        <EditTextPreference
            android:key="rag_api_key"
            android:title="RAG API密钥"
            android:summary="输入向量数据库的API密钥"
            android:inputType="textPassword"
            android:selectAllOnFocus="true"
            android:dependency="enable_rag" />

        <EditTextPreference
            android:key="rag_base_url"
            android:title="RAG服务URL"
            android:summary="向量数据库的服务地址"
            android:inputType="textUri"
            android:selectAllOnFocus="true"
            android:dependency="enable_rag" />

        <EditTextPreference
            android:key="rag_index_name"
            android:title="索引名称"
            android:summary="向量数据库的索引名称"
            android:defaultValue="droidrun-knowledge"
            android:dependency="enable_rag" />

        <EditTextPreference
            android:key="rag_namespace"
            android:title="命名空间"
            android:summary="向量数据库的命名空间"
            android:defaultValue="android-automation"
            android:dependency="enable_rag" />

        <SeekBarPreference
            android:key="rag_top_k"
            android:title="检索数量"
            android:summary="每次检索返回的结果数量"
            android:defaultValue="5"
            android:max="20"
            android:min="1"
            app:showSeekBarValue="true"
            android:dependency="enable_rag" />

        <SeekBarPreference
            android:key="rag_similarity_threshold"
            android:title="相似度阈值"
            android:summary="检索结果的最低相似度要求 (0-100)"
            android:defaultValue="70"
            android:max="100"
            app:showSeekBarValue="true"
            android:dependency="enable_rag" />

    </PreferenceCategory>

    <!-- 性能配置分类 -->
    <PreferenceCategory
        android:title="性能配置"
        android:summary="应用性能和行为设置">

        <SeekBarPreference
            android:key="local_decision_threshold"
            android:title="本地决策阈值"
            android:summary="简单操作使用本地决策的置信度阈值 (0-100)"
            android:defaultValue="80"
            android:max="100"
            app:showSeekBarValue="true" />

        <SeekBarPreference
            android:key="cache_size"
            android:title="缓存大小"
            android:summary="决策和响应缓存的最大条目数"
            android:defaultValue="100"
            android:max="500"
            android:min="10"
            app:showSeekBarValue="true" />

        <SeekBarPreference
            android:key="max_concurrent_tasks"
            android:title="最大并发任务"
            android:summary="同时执行的最大任务数量"
            android:defaultValue="3"
            android:max="10"
            android:min="1"
            app:showSeekBarValue="true" />

        <SwitchPreferenceCompat
            android:key="enable_performance_monitoring"
            android:title="性能监控"
            android:summary="启用实时性能监控和统计"
            android:defaultValue="true" />

        <SwitchPreferenceCompat
            android:key="enable_debug_logging"
            android:title="调试日志"
            android:summary="启用详细的调试日志输出"
            android:defaultValue="false" />

        <EditTextPreference
            android:key="auto_cleanup_days"
            android:title="自动清理天数"
            android:summary="自动清理多少天前的任务和日志"
            android:defaultValue="30"
            android:inputType="number" />

    </PreferenceCategory>

    <!-- 网络配置分类 -->
    <PreferenceCategory
        android:title="网络配置"
        android:summary="网络连接和超时设置">

        <EditTextPreference
            android:key="request_timeout"
            android:title="请求超时"
            android:summary="API请求的超时时间（秒）"
            android:defaultValue="30"
            android:inputType="number" />

        <EditTextPreference
            android:key="max_retries"
            android:title="最大重试次数"
            android:summary="API请求失败时的最大重试次数"
            android:defaultValue="3"
            android:inputType="number" />

        <SwitchPreferenceCompat
            android:key="enable_offline_mode"
            android:title="离线模式"
            android:summary="网络不可用时启用基础离线功能"
            android:defaultValue="true" />

    </PreferenceCategory>

    <!-- 安全配置分类 -->
    <PreferenceCategory
        android:title="安全配置"
        android:summary="隐私和安全相关设置">

        <SwitchPreferenceCompat
            android:key="encrypt_api_keys"
            android:title="加密API密钥"
            android:summary="使用设备加密存储API密钥"
            android:defaultValue="true" />

        <SwitchPreferenceCompat
            android:key="enable_analytics"
            android:title="使用统计"
            android:summary="允许收集匿名使用统计信息"
            android:defaultValue="false" />

        <SwitchPreferenceCompat
            android:key="auto_clear_sensitive_data"
            android:title="自动清理敏感数据"
            android:summary="应用关闭时自动清理敏感信息"
            android:defaultValue="true" />

    </PreferenceCategory>

    <!-- 操作分类 -->
    <PreferenceCategory
        android:title="操作"
        android:summary="配置管理和测试功能">

        <Preference
            android:key="test_connection"
            android:title="测试连接"
            android:summary="测试LLM和RAG服务的连接状态"
            android:icon="@drawable/ic_test_connection" />

        <Preference
            android:key="export_config"
            android:title="导出配置"
            android:summary="导出当前配置到文件"
            android:icon="@drawable/ic_export" />

        <Preference
            android:key="import_config"
            android:title="导入配置"
            android:summary="从文件导入配置"
            android:icon="@drawable/ic_import" />

        <Preference
            android:key="reset_config"
            android:title="重置配置"
            android:summary="恢复所有设置到默认值"
            android:icon="@drawable/ic_reset" />

    </PreferenceCategory>

    <!-- 关于分类 -->
    <PreferenceCategory
        android:title="关于"
        android:summary="应用信息和帮助">

        <Preference
            android:key="app_version"
            android:title="应用版本"
            android:summary="DroidRun高性能版 v1.0.0"
            android:selectable="false" />

        <Preference
            android:key="help_documentation"
            android:title="帮助文档"
            android:summary="查看使用说明和常见问题"
            android:icon="@drawable/ic_help" />

        <Preference
            android:key="github_repo"
            android:title="GitHub仓库"
            android:summary="访问项目源代码和问题反馈"
            android:icon="@drawable/ic_github" />

    </PreferenceCategory>

</PreferenceScreen>
