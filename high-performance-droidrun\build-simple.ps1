# DroidRun Simple Build Script
param([string]$Task = "assembleRelease")

Write-Host "DroidRun Build Tool" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

# Check Java
try {
    $null = java -version 2>&1
    Write-Host "Java: OK" -ForegroundColor Green
} catch {
    Write-Host "Java: ERROR - Please install JDK 11+" -ForegroundColor Red
    exit 1
}

# Check gradlew.bat
if (Test-Path "gradlew.bat") {
    Write-Host "Gradle Wrapper: OK" -ForegroundColor Green
} else {
    Write-Host "Gradle Wrapper: ERROR - gradlew.bat not found" -ForegroundColor Red
    exit 1
}

# Build
Write-Host "Building: $Task" -ForegroundColor Yellow
& .\gradlew.bat $Task --stacktrace

if ($LASTEXITCODE -eq 0) {
    Write-Host "BUILD SUCCESS!" -ForegroundColor Green
    if (Test-Path "app\build\outputs\apk\release\app-release.apk") {
        Write-Host "APK: app\build\outputs\apk\release\app-release.apk" -ForegroundColor Cyan
    }
} else {
    Write-Host "BUILD FAILED!" -ForegroundColor Red
}
