# 🚀 DroidRun豆包服务配置指南

基于您提供的实际API配置，快速设置DroidRun高性能版本。

## 📋 **服务配置总览**

### 🧠 **LLM服务 - DeepSeek**
- **提供商**: DeepSeek
- **API密钥**: `***********************************`
- **API地址**: `https://api.deepseek.com/v1`
- **模型**: `deepseek-chat`

### 🔤 **文本嵌入 - 豆包Embedding**
- **API密钥**: `f891395f-e519-4807-8101-6be9c0b55d4d`
- **API地址**: `https://ark.cn-beijing.volces.com/api/v3/embeddings`
- **模型**: `doubao-embedding-text-240715`
- **备用方案**: 启用

### 🗄️ **向量数据库 - VikingDB**
- **集合名称**: `droidRunRag`
- **主机**: `api-vikingdb.volces.com`
- **区域**: `cn-beijing`
- **访问密钥**: `AKLTYTRmYzFjNTNlMjk1NGFmZGI2ODFkYmQyNjU3Mzk4NDg`
- **秘密密钥**: `WVRjM1l6QmtNV0ZtTUdRNE5HVmhObUU1TkRsa05UZzJOemhpT0RNd01EYw==`

### ☁️ **云知识库**
- **启用状态**: 是
- **集合名称**: `droidRunRag`
- **项目名称**: `default`
- **域名**: `api-knowledgebase.mlp.cn-beijing.volces.com`

## 🔧 **快速配置步骤**

### 1. **应用内配置**

打开DroidRun应用，进入设置页面：

#### **LLM配置**
```
设置 → LLM配置
├── 提供商: DeepSeek
├── API密钥: ***********************************
├── 模型: deepseek-chat
├── 温度: 0.2
└── 最大Token: 512
```

#### **豆包服务配置**
```
设置 → 豆包服务
├── ARK API密钥: f891395f-e519-4807-8101-6be9c0b55d4d
├── VikingDB访问密钥: AKLTYTRmYzFjNTNlMjk1NGFmZGI2ODFkYmQyNjU3Mzk4NDg
└── VikingDB秘密密钥: WVRjM1l6QmtNV0ZtTUdRNE5HVmhObUU1TkRsa05UZzJOemhpT0RNd01EYw==
```

#### **RAG配置**
```
设置 → RAG配置
├── 提供商: VikingDB
├── 集合名称: droidRunRag
├── Top-K: 5
├── 相似度阈值: 0.7
└── 启用云知识库: 是
```

### 2. **配置文件方式**

将以下配置保存为 `doubao_config.json`：

```json
{
  "llm_config": {
    "provider": "deepseek",
    "api_key": "***********************************",
    "api_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat"
  },
  "embedding_config": {
    "api_key": "f891395f-e519-4807-8101-6be9c0b55d4d",
    "api_url": "https://ark.cn-beijing.volces.com/api/v3/embeddings",
    "model": "doubao-embedding-text-240715"
  },
  "vector_db_config": {
    "provider": "viking_db",
    "collection": "droidRunRag",
    "access_key": "AKLTYTRmYzFjNTNlMjk1NGFmZGI2ODFkYmQyNjU3Mzk4NDg",
    "secret_key": "WVRjM1l6QmtNV0ZtTUdRNE5HVmhObUU1TkRsa05UZzJOemhpT0RNd01EYw=="
  },
  "cloud_kb_config": {
    "enabled": true,
    "collection_name": "droidRunRag",
    "domain": "api-knowledgebase.mlp.cn-beijing.volces.com"
  }
}
```

然后在应用中导入配置：
```
设置 → 操作 → 导入配置 → 选择文件
```

### 3. **环境变量方式**

如果使用命令行或开发环境，可以设置环境变量：

```bash
# DeepSeek LLM
export DEEPSEEK_LLM_API_KEY=***********************************
export DEEPSEEK_LLM_API_URL=https://api.deepseek.com/v1
export DEEPSEEK_LLM_MODEL=deepseek-chat

# 豆包Embedding
export ARK_API_KEY=f891395f-e519-4807-8101-6be9c0b55d4d
export ARK_EMBEDDING_MODEL=doubao-embedding-text-240715
export ARK_EMBEDDING_API_URL=https://ark.cn-beijing.volces.com/api/v3/embeddings

# VikingDB向量库
export VIKING_DB_COLLECTION=droidRunRag
export VIKING_DB_HOST=api-vikingdb.volces.com
export VIKING_DB_REGION=cn-beijing
export VIKING_DB_AK=AKLTYTRmYzFjNTNlMjk1NGFmZGI2ODFkYmQyNjU3Mzk4NDg
export VIKING_DB_SK=WVRjM1l6QmtNV0ZtTUdRNE5HVmhObUU1TkRsa05UZzJOemhpT0RNd01EYw==

# 云知识库
export USE_CLOUD_KB=true
export KB_COLLECTION_NAME=droidRunRag
export KB_DOMAIN=api-knowledgebase.mlp.cn-beijing.volces.com
```

## ✅ **配置验证**

### **测试连接**
在应用设置中点击"测试连接"，验证各服务状态：

- ✅ **DeepSeek LLM**: 测试基础对话功能
- ✅ **豆包Embedding**: 测试文本向量化
- ✅ **VikingDB**: 测试向量搜索
- ✅ **云知识库**: 测试知识检索

### **预期结果**
```json
{
  "deepseek_llm": true,
  "doubao_embedding": true, 
  "viking_db": true,
  "cloud_kb": true
}
```

## 🚀 **开始使用**

配置完成后，您可以：

### **基础对话测试**
```
输入: "你好，请介绍一下自己"
预期: DeepSeek模型回复介绍信息
```

### **知识检索测试**
```
输入: "如何点击Android按钮"
预期: 从VikingDB检索相关知识并回答
```

### **UI自动化测试**
```
输入: "打开微信"
预期: 系统分析UI状态并执行打开操作
```

## 🔧 **高级配置**

### **性能优化**
```json
{
  "local_decision_threshold": 0.8,
  "cache_size": 100,
  "max_concurrent_tasks": 3,
  "enable_performance_monitoring": true
}
```

### **安全设置**
```json
{
  "encrypt_api_keys": true,
  "auto_clear_sensitive_data": true,
  "enable_analytics": false
}
```

### **备用方案**
```json
{
  "use_fallback_embedding": true,
  "enable_local_fallback": true,
  "fallback_threshold_ms": 10000
}
```

## 🛠️ **故障排除**

### **常见问题**

**Q: DeepSeek API调用失败**
```
检查项目:
1. API密钥是否正确
2. 网络连接是否正常
3. API配额是否充足
```

**Q: VikingDB连接超时**
```
解决方案:
1. 检查访问密钥和秘密密钥
2. 确认集合名称正确
3. 检查网络防火墙设置
```

**Q: 嵌入向量生成失败**
```
备用方案:
1. 启用本地嵌入模型
2. 使用缓存的向量结果
3. 降级到纯文本匹配
```

### **日志查看**
```bash
# 查看应用日志
adb logcat -s DroidRunHP

# 查看豆包服务日志
adb logcat -s DoubaoService

# 查看性能日志
adb logcat -s PerformanceProfiler
```

## 📞 **技术支持**

如遇问题，请提供以下信息：
- 应用版本号
- 错误日志
- 配置信息（隐藏敏感密钥）
- 设备型号和Android版本

---

**配置完成后，DroidRun将具备强大的AI驱动自动化能力！** 🎉
