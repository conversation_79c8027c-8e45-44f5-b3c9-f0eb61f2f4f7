package com.droidrun.hp.ui

import android.os.Bundle
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.preference.*
import com.droidrun.hp.R
import com.droidrun.hp.config.DroidRunConfig
import com.droidrun.hp.data.model.LLMProvider
import com.droidrun.hp.data.model.RAGProvider
import com.droidrun.hp.ui.viewmodel.SettingsViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 设置Activity
 * 管理LLM、RAG和应用配置
 */
class SettingsActivity : AppCompatActivity() {
    
    private val viewModel: SettingsViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)
        
        setupToolbar()
        
        if (savedInstanceState == null) {
            supportFragmentManager
                .beginTransaction()
                .replace(R.id.settings_container, SettingsFragment())
                .commit()
        }
    }
    
    private fun setupToolbar() {
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "设置"
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    /**
     * 设置Fragment
     */
    class SettingsFragment : PreferenceFragmentCompat() {
        
        private lateinit var config: DroidRunConfig
        
        override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
            setPreferencesFromResource(R.xml.preferences, rootKey)
            
            config = DroidRunConfig(requireContext())
            
            setupLLMPreferences()
            setupRAGPreferences()
            setupAppPreferences()
            observeConfig()
        }
        
        /**
         * 设置LLM相关配置
         */
        private fun setupLLMPreferences() {
            // LLM提供商选择
            findPreference<ListPreference>("llm_provider")?.apply {
                entries = arrayOf("OpenAI", "Anthropic", "Google Gemini", "Ollama", "自定义")
                entryValues = arrayOf("openai", "anthropic", "gemini", "ollama", "custom")
                
                setOnPreferenceChangeListener { _, newValue ->
                    val provider = LLMProvider.valueOf(newValue.toString().uppercase())
                    lifecycleScope.launch {
                        config.setLLMProvider(provider)
                        updateLLMModelOptions(provider)
                    }
                    true
                }
            }
            
            // LLM API密钥
            findPreference<EditTextPreference>("llm_api_key")?.apply {
                setOnBindEditTextListener { editText ->
                    editText.inputType = android.text.InputType.TYPE_CLASS_TEXT or 
                                        android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD
                }
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        config.setLLMApiKey(newValue.toString())
                    }
                    true
                }
            }
            
            // LLM基础URL
            findPreference<EditTextPreference>("llm_base_url")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.llmConfig.collect { it }
                        config.updateLLMConfig(currentConfig.copy(baseUrl = newValue.toString()))
                    }
                    true
                }
            }
            
            // LLM模型选择
            findPreference<ListPreference>("llm_model")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.llmConfig.collect { it }
                        config.updateLLMConfig(currentConfig.copy(model = newValue.toString()))
                    }
                    true
                }
            }
            
            // 温度设置
            findPreference<SeekBarPreference>("llm_temperature")?.apply {
                min = 0
                max = 100
                showSeekBarValue = true
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val temperature = (newValue as Int) / 100f
                        val currentConfig = config.llmConfig.collect { it }
                        config.updateLLMConfig(currentConfig.copy(temperature = temperature))
                    }
                    true
                }
            }
            
            // 最大Token数
            findPreference<EditTextPreference>("llm_max_tokens")?.apply {
                setOnBindEditTextListener { editText ->
                    editText.inputType = android.text.InputType.TYPE_CLASS_NUMBER
                }
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val maxTokens = newValue.toString().toIntOrNull() ?: 512
                        val currentConfig = config.llmConfig.collect { it }
                        config.updateLLMConfig(currentConfig.copy(maxTokens = maxTokens))
                    }
                    true
                }
            }
        }
        
        /**
         * 设置RAG相关配置
         */
        private fun setupRAGPreferences() {
            // RAG提供商选择
            findPreference<ListPreference>("rag_provider")?.apply {
                entries = arrayOf("Pinecone", "Weaviate", "Qdrant", "自定义")
                entryValues = arrayOf("pinecone", "weaviate", "qdrant", "custom")
                
                setOnPreferenceChangeListener { _, newValue ->
                    val provider = RAGProvider.valueOf(newValue.toString().uppercase())
                    lifecycleScope.launch {
                        config.setRAGProvider(provider)
                        updateRAGUrlHint(provider)
                    }
                    true
                }
            }
            
            // RAG API密钥
            findPreference<EditTextPreference>("rag_api_key")?.apply {
                setOnBindEditTextListener { editText ->
                    editText.inputType = android.text.InputType.TYPE_CLASS_TEXT or 
                                        android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD
                }
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        config.setRAGApiKey(newValue.toString())
                    }
                    true
                }
            }
            
            // RAG基础URL
            findPreference<EditTextPreference>("rag_base_url")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.ragConfig.collect { it }
                        config.updateRAGConfig(currentConfig.copy(baseUrl = newValue.toString()))
                    }
                    true
                }
            }
            
            // 索引名称
            findPreference<EditTextPreference>("rag_index_name")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.ragConfig.collect { it }
                        config.updateRAGConfig(currentConfig.copy(indexName = newValue.toString()))
                    }
                    true
                }
            }
            
            // 命名空间
            findPreference<EditTextPreference>("rag_namespace")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.ragConfig.collect { it }
                        config.updateRAGConfig(currentConfig.copy(namespace = newValue.toString()))
                    }
                    true
                }
            }
            
            // Top-K设置
            findPreference<SeekBarPreference>("rag_top_k")?.apply {
                min = 1
                max = 20
                showSeekBarValue = true
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.ragConfig.collect { it }
                        config.updateRAGConfig(currentConfig.copy(topK = newValue as Int))
                    }
                    true
                }
            }
            
            // 相似度阈值
            findPreference<SeekBarPreference>("rag_similarity_threshold")?.apply {
                min = 0
                max = 100
                showSeekBarValue = true
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val threshold = (newValue as Int) / 100f
                        val currentConfig = config.ragConfig.collect { it }
                        config.updateRAGConfig(currentConfig.copy(similarityThreshold = threshold))
                    }
                    true
                }
            }
        }
        
        /**
         * 设置应用相关配置
         */
        private fun setupAppPreferences() {
            // 本地决策阈值
            findPreference<SeekBarPreference>("local_decision_threshold")?.apply {
                min = 0
                max = 100
                showSeekBarValue = true
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val threshold = (newValue as Int) / 100f
                        val currentConfig = config.appConfig.collect { it }
                        config.updateAppConfig(currentConfig.copy(localDecisionThreshold = threshold))
                    }
                    true
                }
            }
            
            // 缓存大小
            findPreference<SeekBarPreference>("cache_size")?.apply {
                min = 10
                max = 500
                showSeekBarValue = true
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.appConfig.collect { it }
                        config.updateAppConfig(currentConfig.copy(cacheSize = newValue as Int))
                    }
                    true
                }
            }
            
            // 最大并发任务数
            findPreference<SeekBarPreference>("max_concurrent_tasks")?.apply {
                min = 1
                max = 10
                showSeekBarValue = true
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.appConfig.collect { it }
                        config.updateAppConfig(currentConfig.copy(maxConcurrentTasks = newValue as Int))
                    }
                    true
                }
            }
            
            // 性能监控开关
            findPreference<SwitchPreferenceCompat>("enable_performance_monitoring")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.appConfig.collect { it }
                        config.updateAppConfig(currentConfig.copy(enablePerformanceMonitoring = newValue as Boolean))
                    }
                    true
                }
            }
            
            // 调试日志开关
            findPreference<SwitchPreferenceCompat>("enable_debug_logging")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val currentConfig = config.appConfig.collect { it }
                        config.updateAppConfig(currentConfig.copy(enableDebugLogging = newValue as Boolean))
                    }
                    true
                }
            }
            
            // 自动清理天数
            findPreference<EditTextPreference>("auto_cleanup_days")?.apply {
                setOnBindEditTextListener { editText ->
                    editText.inputType = android.text.InputType.TYPE_CLASS_NUMBER
                }
                
                setOnPreferenceChangeListener { _, newValue ->
                    lifecycleScope.launch {
                        val days = newValue.toString().toIntOrNull() ?: 30
                        val currentConfig = config.appConfig.collect { it }
                        config.updateAppConfig(currentConfig.copy(autoCleanupDays = days))
                    }
                    true
                }
            }
            
            // 测试连接按钮
            findPreference<Preference>("test_connection")?.apply {
                setOnPreferenceClickListener {
                    testConnections()
                    true
                }
            }
            
            // 重置配置按钮
            findPreference<Preference>("reset_config")?.apply {
                setOnPreferenceClickListener {
                    showResetConfirmDialog()
                    true
                }
            }
            
            // 导出配置按钮
            findPreference<Preference>("export_config")?.apply {
                setOnPreferenceClickListener {
                    exportConfiguration()
                    true
                }
            }
        }
        
        /**
         * 观察配置变化
         */
        private fun observeConfig() {
            lifecycleScope.launch {
                config.llmConfig.collect { llmConfig ->
                    updateLLMPreferences(llmConfig)
                }
            }
            
            lifecycleScope.launch {
                config.ragConfig.collect { ragConfig ->
                    updateRAGPreferences(ragConfig)
                }
            }
            
            lifecycleScope.launch {
                config.appConfig.collect { appConfig ->
                    updateAppPreferences(appConfig)
                }
            }
        }
        
        /**
         * 更新LLM配置显示
         */
        private fun updateLLMPreferences(llmConfig: DroidRunConfig.LLMConfig) {
            findPreference<ListPreference>("llm_provider")?.value = llmConfig.provider.name.lowercase()
            findPreference<EditTextPreference>("llm_api_key")?.text = llmConfig.apiKey
            findPreference<EditTextPreference>("llm_base_url")?.text = llmConfig.baseUrl
            findPreference<ListPreference>("llm_model")?.value = llmConfig.model
            findPreference<SeekBarPreference>("llm_temperature")?.value = (llmConfig.temperature * 100).toInt()
            findPreference<EditTextPreference>("llm_max_tokens")?.text = llmConfig.maxTokens.toString()
            
            updateLLMModelOptions(llmConfig.provider)
        }
        
        /**
         * 更新RAG配置显示
         */
        private fun updateRAGPreferences(ragConfig: DroidRunConfig.RAGConfig) {
            findPreference<ListPreference>("rag_provider")?.value = ragConfig.provider.name.lowercase()
            findPreference<EditTextPreference>("rag_api_key")?.text = ragConfig.apiKey
            findPreference<EditTextPreference>("rag_base_url")?.text = ragConfig.baseUrl
            findPreference<EditTextPreference>("rag_index_name")?.text = ragConfig.indexName
            findPreference<EditTextPreference>("rag_namespace")?.text = ragConfig.namespace
            findPreference<SeekBarPreference>("rag_top_k")?.value = ragConfig.topK
            findPreference<SeekBarPreference>("rag_similarity_threshold")?.value = (ragConfig.similarityThreshold * 100).toInt()
            
            updateRAGUrlHint(ragConfig.provider)
        }
        
        /**
         * 更新应用配置显示
         */
        private fun updateAppPreferences(appConfig: DroidRunConfig.AppConfig) {
            findPreference<SeekBarPreference>("local_decision_threshold")?.value = (appConfig.localDecisionThreshold * 100).toInt()
            findPreference<SeekBarPreference>("cache_size")?.value = appConfig.cacheSize
            findPreference<SeekBarPreference>("max_concurrent_tasks")?.value = appConfig.maxConcurrentTasks
            findPreference<SwitchPreferenceCompat>("enable_performance_monitoring")?.isChecked = appConfig.enablePerformanceMonitoring
            findPreference<SwitchPreferenceCompat>("enable_debug_logging")?.isChecked = appConfig.enableDebugLogging
            findPreference<EditTextPreference>("auto_cleanup_days")?.text = appConfig.autoCleanupDays.toString()
        }
        
        /**
         * 更新LLM模型选项
         */
        private fun updateLLMModelOptions(provider: LLMProvider) {
            findPreference<ListPreference>("llm_model")?.apply {
                when (provider) {
                    LLMProvider.OPENAI -> {
                        entries = arrayOf("GPT-4o", "GPT-4o-mini", "GPT-4-turbo", "GPT-3.5-turbo")
                        entryValues = arrayOf("gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo")
                    }
                    LLMProvider.ANTHROPIC -> {
                        entries = arrayOf("Claude-3.5-Sonnet", "Claude-3-Haiku", "Claude-3-Opus")
                        entryValues = arrayOf("claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229")
                    }
                    LLMProvider.GEMINI -> {
                        entries = arrayOf("Gemini-1.5-Pro", "Gemini-1.5-Flash", "Gemini-1.0-Pro")
                        entryValues = arrayOf("gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro")
                    }
                    LLMProvider.OLLAMA -> {
                        entries = arrayOf("Llama3.1:8b", "Llama3.1:70b", "Qwen2:7b", "Phi3:mini")
                        entryValues = arrayOf("llama3.1:8b", "llama3.1:70b", "qwen2:7b", "phi3:mini")
                    }
                    LLMProvider.CUSTOM -> {
                        entries = arrayOf("自定义模型")
                        entryValues = arrayOf("custom-model")
                    }
                }
            }
        }
        
        /**
         * 更新RAG URL提示
         */
        private fun updateRAGUrlHint(provider: RAGProvider) {
            findPreference<EditTextPreference>("rag_base_url")?.apply {
                dialogMessage = when (provider) {
                    RAGProvider.PINECONE -> "输入Pinecone索引URL，格式：https://your-index-xxx.svc.environment.pinecone.io"
                    RAGProvider.WEAVIATE -> "输入Weaviate集群URL，格式：https://your-cluster.weaviate.network"
                    RAGProvider.QDRANT -> "输入Qdrant集群URL，格式：https://your-cluster.qdrant.io"
                    RAGProvider.CUSTOM -> "输入自定义RAG服务URL"
                }
            }
        }
        
        /**
         * 测试连接
         */
        private fun testConnections() {
            // TODO: 实现连接测试逻辑
            MaterialAlertDialogBuilder(requireContext())
                .setTitle("测试连接")
                .setMessage("正在测试LLM和RAG服务连接...")
                .setPositiveButton("确定", null)
                .show()
        }
        
        /**
         * 显示重置确认对话框
         */
        private fun showResetConfirmDialog() {
            MaterialAlertDialogBuilder(requireContext())
                .setTitle("重置配置")
                .setMessage("确定要重置所有配置到默认值吗？此操作不可撤销。")
                .setPositiveButton("重置") { _, _ ->
                    lifecycleScope.launch {
                        config.resetToDefaults()
                    }
                }
                .setNegativeButton("取消", null)
                .show()
        }
        
        /**
         * 导出配置
         */
        private fun exportConfiguration() {
            lifecycleScope.launch {
                try {
                    val configMap = config.exportConfig()
                    // TODO: 实现配置导出逻辑（保存到文件或分享）
                    Timber.d("导出配置: $configMap")
                } catch (e: Exception) {
                    Timber.e(e, "导出配置失败")
                }
            }
        }
    }
}
