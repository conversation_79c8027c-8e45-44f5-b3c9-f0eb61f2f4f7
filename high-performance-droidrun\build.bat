@echo off
chcp 65001 >nul
title DroidRun高性能版构建工具

echo.
echo 🚀 DroidRun高性能版构建工具
echo ================================
echo.

REM 检查参数
if "%1"=="help" goto :help
if "%1"=="--help" goto :help
if "%1"=="-h" goto :help

REM 设置默认任务
set TASK=assembleDebug
if not "%1"=="" set TASK=%1

echo 📋 检查构建环境...
echo.

REM 检查Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java
    echo    请安装JDK 11或更高版本
    echo    下载地址: https://adoptium.net/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Java环境正常
)

REM 检查Android SDK
if defined ANDROID_HOME (
    echo ✅ Android SDK: %ANDROID_HOME%
) else (
    echo ⚠️  警告: 未设置ANDROID_HOME环境变量
    echo    请安装Android Studio并设置SDK路径
)

REM 检查Gradle Wrapper
if exist "gradlew.bat" (
    echo ✅ Gradle Wrapper: 已找到
) else (
    echo ❌ 错误: 未找到gradlew.bat
    echo    正在尝试生成...
    gradle wrapper --gradle-version 8.4 >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Gradle Wrapper生成成功
    ) else (
        echo ❌ 无法生成Gradle Wrapper
        echo    请手动安装Gradle或使用Android Studio
        pause
        exit /b 1
    )
)

echo.
echo 🔨 开始构建任务: %TASK%
echo ================================
echo.

REM 执行构建
call gradlew.bat %TASK% --stacktrace

if %errorlevel% equ 0 (
    echo.
    echo ================================
    echo 🎉 构建成功完成！
    echo.
    
    REM 显示APK位置
    if "%TASK%"=="assembleDebug" (
        echo 📦 调试APK位置:
        if exist "app\build\outputs\apk\debug\app-debug.apk" (
            echo    app\build\outputs\apk\debug\app-debug.apk
        )
    )
    
    if "%TASK%"=="assembleRelease" (
        echo 📦 发布APK位置:
        if exist "app\build\outputs\apk\release\app-release.apk" (
            echo    app\build\outputs\apk\release\app-release.apk
        )
    )
    
    echo.
    echo 🔧 下一步操作:
    echo    1. 安装APK到设备: adb install [APK路径]
    echo    2. 启动应用并配置豆包服务
    echo    3. 参考 DOUBAO_SETUP_GUIDE.md 进行配置
    echo.
    
) else (
    echo.
    echo ================================
    echo ❌ 构建失败
    echo.
    echo 💡 常见解决方案:
    echo    1. 检查网络连接
    echo    2. 清理项目: build.bat clean
    echo    3. 检查Android SDK配置
    echo    4. 更新Android Studio
    echo.
)

echo 按任意键退出...
pause >nul
exit /b %errorlevel%

:help
echo.
echo DroidRun高性能版构建工具
echo.
echo 用法:
echo   build.bat [任务名]
echo.
echo 常用任务:
echo   assembleDebug     构建调试版本 (默认)
echo   assembleRelease   构建发布版本
echo   clean             清理构建文件
echo   test              运行单元测试
echo.
echo 示例:
echo   build.bat                    构建调试版本
echo   build.bat assembleRelease    构建发布版本
echo   build.bat clean              清理项目
echo.
echo 其他选项:
echo   build.bat help               显示此帮助
echo.
pause
exit /b 0
