@echo off
echo Installing Gradle for DroidRun Build
echo =====================================

echo Step 1: Checking if Gradle is already installed...
gradle --version >nul 2>&1
if %errorlevel% equ 0 (
    echo OK: Gradle is already installed
    gradle --version
    goto :build
)

echo Step 2: Downloading and installing Gradle...
echo This will download Gradle 8.4 to C:\gradle

REM Create gradle directory
if not exist "C:\gradle" mkdir C:\gradle

echo Downloading Gradle 8.4...
powershell -Command "& {try { Write-Host 'Downloading Gradle 8.4...'; Invoke-WebRequest -Uri 'https://services.gradle.org/distributions/gradle-8.4-bin.zip' -OutFile 'C:\gradle\gradle-8.4-bin.zip' -UseBasicParsing; Write-Host 'Download completed' } catch { Write-Host 'Download failed: ' + $_.Exception.Message; exit 1 }}"

if not exist "C:\gradle\gradle-8.4-bin.zip" (
    echo ERROR: Failed to download Gradle
    echo Please download manually from: https://gradle.org/releases/
    pause
    exit /b 1
)

echo Extracting Gradle...
powershell -Command "& {try { Expand-Archive -Path 'C:\gradle\gradle-8.4-bin.zip' -DestinationPath 'C:\gradle' -Force; Write-Host 'Extraction completed' } catch { Write-Host 'Extraction failed: ' + $_.Exception.Message; exit 1 }}"

echo Setting up Gradle...
set GRADLE_HOME=C:\gradle\gradle-8.4
set PATH=%GRADLE_HOME%\bin;%PATH%

echo Verifying Gradle installation...
C:\gradle\gradle-8.4\bin\gradle --version
if %errorlevel% neq 0 (
    echo ERROR: Gradle installation failed
    pause
    exit /b 1
)

echo.
echo SUCCESS: Gradle installed successfully!
echo.
echo To make this permanent, add to your system PATH:
echo   C:\gradle\gradle-8.4\bin
echo.

:build
echo Step 3: Building DroidRun with Gradle...
echo ========================================

REM Set Android SDK if not set
if not defined ANDROID_HOME (
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
        echo Set ANDROID_HOME to: %ANDROID_HOME%
    )
)

echo Cleaning project...
C:\gradle\gradle-8.4\bin\gradle clean

echo Building release APK...
C:\gradle\gradle-8.4\bin\gradle assembleRelease

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESS!
    echo ========================================
    echo.
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK created successfully:
        echo   app\build\outputs\apk\release\app-release.apk
        echo.
        echo File size:
        for %%A in ("app\build\outputs\apk\release\app-release.apk") do echo   %%~zA bytes
        echo.
        echo Next steps:
        echo   1. Install APK: adb install app\build\outputs\apk\release\app-release.apk
        echo   2. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)
        echo   3. Enable accessibility service in Android settings
    ) else (
        echo WARNING: APK file not found in expected location
        echo Searching for APK files...
        dir /s *.apk 2>nul | find ".apk"
    )
) else (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo Common issues and solutions:
    echo   1. Network connection - check internet access
    echo   2. Android SDK - install Android Studio
    echo   3. Java version - ensure JDK 11+ is installed
    echo   4. Disk space - ensure sufficient free space
    echo.
    echo For detailed troubleshooting, see BUILD_GUIDE.md
)

echo.
pause
