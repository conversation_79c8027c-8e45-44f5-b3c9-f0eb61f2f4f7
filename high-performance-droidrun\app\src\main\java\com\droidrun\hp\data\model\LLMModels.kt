package com.droidrun.hp.data.model

import kotlinx.serialization.Serializable

/**
 * LLM提供商枚举 - 简化版本，只保留必要的服务商
 */
enum class LLMProvider {
    DOUBAO,    // 豆包（字节跳动）- 主要推荐
    OPENAI,    // OpenAI - 备选方案
    OLLAMA,    // 本地模型 - 离线方案
    CUSTOM     // 自定义 - 扩展性
}

/**
 * RAG提供商枚举 - 简化版本
 */
enum class RAGProvider {
    DOUBAO_VECTOR,  // 豆包向量数据库
    PINECONE,       // Pinecone - 备选方案
    CUSTOM          // 自定义
}

/**
 * LLM请求数据模型
 */
@Serializable
data class LLMRequest(
    val prompt: String,
    val model: String,
    val maxTokens: Int = 512,
    val temperature: Float = 0.2f,
    val provider: LLMProvider,
    val apiKey: String,
    val baseUrl: String? = null,
    val systemPrompt: String? = null,
    val stream: Boolean = false
)

/**
 * LLM响应数据模型
 */
@Serializable
data class LLMResponse(
    val id: String,
    val content: String,
    val model: String,
    val usage: LLMUsage,
    val finishReason: String,
    val created: Long = System.currentTimeMillis()
)

/**
 * LLM使用统计
 */
@Serializable
data class LLMUsage(
    val promptTokens: Int,
    val completionTokens: Int,
    val totalTokens: Int
)

/**
 * 豆包API请求格式
 */
@Serializable
data class DoubaoRequest(
    val model: String,
    val messages: List<DoubaoMessage>,
    val temperature: Float = 0.2f,
    val max_tokens: Int = 512,
    val stream: Boolean = false,
    val top_p: Float = 0.9f
)

/**
 * 豆包消息格式
 */
@Serializable
data class DoubaoMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)

/**
 * 豆包API响应格式
 */
@Serializable
data class DoubaoResponse(
    val id: String,
    val object: String,
    val created: Long,
    val model: String,
    val choices: List<DoubaoChoice>,
    val usage: DoubaoUsage
)

/**
 * 豆包选择项
 */
@Serializable
data class DoubaoChoice(
    val index: Int,
    val message: DoubaoMessage,
    val finish_reason: String
)

/**
 * 豆包使用统计
 */
@Serializable
data class DoubaoUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

/**
 * OpenAI请求格式（保持兼容）
 */
@Serializable
data class OpenAIRequest(
    val model: String,
    val messages: List<OpenAIMessage>,
    val temperature: Float = 0.2f,
    val maxTokens: Int = 512,
    val stream: Boolean = false
)

/**
 * OpenAI消息格式
 */
@Serializable
data class OpenAIMessage(
    val role: String,
    val content: String
)

/**
 * OpenAI响应格式
 */
@Serializable
data class OpenAIResponse(
    val id: String,
    val object: String,
    val created: Long,
    val model: String,
    val choices: List<OpenAIChoice>,
    val usage: OpenAIUsage?
)

/**
 * OpenAI选择项
 */
@Serializable
data class OpenAIChoice(
    val index: Int,
    val message: OpenAIMessage,
    val finishReason: String
)

/**
 * OpenAI使用统计
 */
@Serializable
data class OpenAIUsage(
    val promptTokens: Int,
    val completionTokens: Int,
    val totalTokens: Int
)

/**
 * Ollama请求格式
 */
@Serializable
data class OllamaRequest(
    val model: String,
    val prompt: String,
    val stream: Boolean = false,
    val options: OllamaOptions? = null
)

/**
 * Ollama选项
 */
@Serializable
data class OllamaOptions(
    val temperature: Float = 0.2f,
    val numPredict: Int = 512
)

/**
 * Ollama响应格式
 */
@Serializable
data class OllamaResponse(
    val model: String,
    val response: String,
    val done: Boolean,
    val context: List<Int>? = null
)

/**
 * RAG查询扩展 - 添加豆包向量库支持
 */
@Serializable
data class RAGQuery(
    val text: String,
    val category: String? = null,
    val tags: Set<String>? = null,
    val maxResults: Int? = null,
    val similarityThreshold: Float? = null,
    val contextLength: Int? = null,
    val provider: RAGProvider = RAGProvider.DOUBAO_VECTOR,
    val apiKey: String = "",
    val baseUrl: String = "",
    val collection: String = "android_knowledge",
    val embedding: List<Float>? = null,
    val filters: Map<String, Any>? = null
)

/**
 * 豆包向量数据库请求
 */
@Serializable
data class DoubaoVectorRequest(
    val query: String,
    val collection: String,
    val top_k: Int = 5,
    val threshold: Float = 0.7f,
    val filters: Map<String, Any>? = null
)

/**
 * 豆包向量数据库响应
 */
@Serializable
data class DoubaoVectorResponse(
    val results: List<DoubaoVectorResult>,
    val total: Int,
    val query_time_ms: Long
)

/**
 * 豆包向量搜索结果
 */
@Serializable
data class DoubaoVectorResult(
    val id: String,
    val score: Float,
    val metadata: Map<String, Any>,
    val content: String
)

/**
 * 混合引擎配置 - 简化版本
 */
@Serializable
data class HybridEngineConfig(
    val llmProvider: LLMProvider = LLMProvider.DOUBAO,
    val ragProvider: RAGProvider = RAGProvider.DOUBAO_VECTOR,
    val apiKey: String,
    val baseUrl: String = "",
    val enableLocalDecision: Boolean = true,
    val localDecisionThreshold: Float = 0.8f,
    val enableRAG: Boolean = true,
    val cacheEnabled: Boolean = true
)

/**
 * 决策请求
 */
@Serializable
data class DecisionRequest(
    val goal: String,
    val currentUIState: UIState? = null,
    val history: List<AgentStep> = emptyList(),
    val context: Map<String, String> = emptyMap()
)

/**
 * 决策响应
 */
@Serializable
data class DecisionResponse(
    val strategy: DecisionStrategy,
    val confidence: Float,
    val actions: List<UIAction>,
    val reasoning: String,
    val estimatedDuration: Long,
    val usedRAG: Boolean = false,
    val cacheHit: Boolean = false
)

/**
 * 决策策略
 */
enum class DecisionStrategy {
    LOCAL,    // 本地决策
    CLOUD,    // 云端决策
    HYBRID    // 混合决策
}

/**
 * 混合引擎统计
 */
@Serializable
data class HybridEngineStats(
    val isInitialized: Boolean,
    val runningTaskCount: Int,
    val totalRequests: Long,
    val localDecisions: Long,
    val cloudDecisions: Long,
    val cacheHitRate: Float,
    val averageResponseTime: Long
)
