package com.droidrun.hp.data.model

import kotlinx.serialization.Serializable

/**
 * LLM提供商枚举 - 基于实际可用服务
 */
enum class LLMProvider {
    DEEPSEEK,  // DeepSeek - 主要推荐
    OPENAI,    // OpenAI - 备选方案
    OLLAMA,    // 本地模型 - 离线方案
    CUSTOM     // 自定义 - 扩展性
}

/**
 * RAG提供商枚举 - 基于字节跳动生态
 */
enum class RAGProvider {
    VIKING_DB,      // VikingDB向量数据库 - 主要推荐
    CLOUD_KB,       // 云知识库
    PINECONE,       // Pinecone - 备选方案
    CUSTOM          // 自定义
}

/**
 * LLM请求数据模型
 */
@Serializable
data class LLMRequest(
    val prompt: String,
    val model: String,
    val maxTokens: Int = 512,
    val temperature: Float = 0.2f,
    val provider: LLMProvider,
    val apiKey: String,
    val baseUrl: String? = null,
    val systemPrompt: String? = null,
    val stream: Boolean = false
)

/**
 * LLM响应数据模型
 */
@Serializable
data class LLMResponse(
    val id: String,
    val content: String,
    val model: String,
    val usage: LLMUsage,
    val finishReason: String,
    val created: Long = System.currentTimeMillis()
)

/**
 * LLM使用统计
 */
@Serializable
data class LLMUsage(
    val promptTokens: Int,
    val completionTokens: Int,
    val totalTokens: Int
)

/**
 * DeepSeek API请求格式
 */
@Serializable
data class DeepSeekRequest(
    val model: String,
    val messages: List<DeepSeekMessage>,
    val temperature: Float = 0.2f,
    val max_tokens: Int = 512,
    val stream: Boolean = false,
    val top_p: Float = 0.9f
)

/**
 * DeepSeek消息格式
 */
@Serializable
data class DeepSeekMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)

/**
 * DeepSeek API响应格式
 */
@Serializable
data class DeepSeekResponse(
    val id: String,
    val object: String,
    val created: Long,
    val model: String,
    val choices: List<DeepSeekChoice>,
    val usage: DeepSeekUsage
)

/**
 * DeepSeek选择项
 */
@Serializable
data class DeepSeekChoice(
    val index: Int,
    val message: DeepSeekMessage,
    val finish_reason: String
)

/**
 * DeepSeek使用统计
 */
@Serializable
data class DeepSeekUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

/**
 * OpenAI请求格式（保持兼容）
 */
@Serializable
data class OpenAIRequest(
    val model: String,
    val messages: List<OpenAIMessage>,
    val temperature: Float = 0.2f,
    val maxTokens: Int = 512,
    val stream: Boolean = false
)

/**
 * OpenAI消息格式
 */
@Serializable
data class OpenAIMessage(
    val role: String,
    val content: String
)

/**
 * OpenAI响应格式
 */
@Serializable
data class OpenAIResponse(
    val id: String,
    val object: String,
    val created: Long,
    val model: String,
    val choices: List<OpenAIChoice>,
    val usage: OpenAIUsage?
)

/**
 * OpenAI选择项
 */
@Serializable
data class OpenAIChoice(
    val index: Int,
    val message: OpenAIMessage,
    val finishReason: String
)

/**
 * OpenAI使用统计
 */
@Serializable
data class OpenAIUsage(
    val promptTokens: Int,
    val completionTokens: Int,
    val totalTokens: Int
)

/**
 * Ollama请求格式
 */
@Serializable
data class OllamaRequest(
    val model: String,
    val prompt: String,
    val stream: Boolean = false,
    val options: OllamaOptions? = null
)

/**
 * Ollama选项
 */
@Serializable
data class OllamaOptions(
    val temperature: Float = 0.2f,
    val numPredict: Int = 512
)

/**
 * Ollama响应格式
 */
@Serializable
data class OllamaResponse(
    val model: String,
    val response: String,
    val done: Boolean,
    val context: List<Int>? = null
)

/**
 * RAG查询扩展 - 添加豆包向量库支持
 */
@Serializable
data class RAGQuery(
    val text: String,
    val category: String? = null,
    val tags: Set<String>? = null,
    val maxResults: Int? = null,
    val similarityThreshold: Float? = null,
    val contextLength: Int? = null,
    val provider: RAGProvider = RAGProvider.DOUBAO_VECTOR,
    val apiKey: String = "",
    val baseUrl: String = "",
    val collection: String = "android_knowledge",
    val embedding: List<Float>? = null,
    val filters: Map<String, Any>? = null
)

/**
 * 豆包Embedding API请求
 */
@Serializable
data class DoubaoEmbeddingRequest(
    val model: String,
    val input: List<String>,
    val encoding_format: String = "float"
)

/**
 * 豆包Embedding API响应
 */
@Serializable
data class DoubaoEmbeddingResponse(
    val object: String,
    val data: List<DoubaoEmbeddingData>,
    val model: String,
    val usage: DoubaoEmbeddingUsage
)

/**
 * 豆包Embedding数据
 */
@Serializable
data class DoubaoEmbeddingData(
    val object: String,
    val index: Int,
    val embedding: List<Float>
)

/**
 * 豆包Embedding使用统计
 */
@Serializable
data class DoubaoEmbeddingUsage(
    val prompt_tokens: Int,
    val total_tokens: Int
)

/**
 * VikingDB向量搜索请求
 */
@Serializable
data class VikingDBSearchRequest(
    val collection: String,
    val vector: List<Float>,
    val limit: Int = 5,
    val filter: Map<String, Any>? = null,
    val output_fields: List<String>? = null
)

/**
 * VikingDB向量搜索响应
 */
@Serializable
data class VikingDBSearchResponse(
    val code: Int,
    val message: String,
    val data: List<VikingDBSearchResult>
)

/**
 * VikingDB搜索结果
 */
@Serializable
data class VikingDBSearchResult(
    val id: String,
    val score: Float,
    val fields: Map<String, Any>
)

/**
 * 云知识库查询请求
 */
@Serializable
data class CloudKBQueryRequest(
    val query: String,
    val collection_name: String,
    val top_k: Int = 5,
    val score_threshold: Float = 0.7f
)

/**
 * 云知识库查询响应
 */
@Serializable
data class CloudKBQueryResponse(
    val code: Int,
    val message: String,
    val data: CloudKBQueryData
)

/**
 * 云知识库查询数据
 */
@Serializable
data class CloudKBQueryData(
    val chunks: List<CloudKBChunk>,
    val total: Int
)

/**
 * 云知识库文档块
 */
@Serializable
data class CloudKBChunk(
    val chunk_id: String,
    val content: String,
    val score: Float,
    val metadata: Map<String, Any>
)

/**
 * 混合引擎配置 - 基于实际服务
 */
@Serializable
data class HybridEngineConfig(
    val llmProvider: LLMProvider = LLMProvider.DEEPSEEK,
    val ragProvider: RAGProvider = RAGProvider.VIKING_DB,
    val deepseekApiKey: String = "",
    val arkApiKey: String = "",
    val vikingDbAk: String = "",
    val vikingDbSk: String = "",
    val enableLocalDecision: Boolean = true,
    val localDecisionThreshold: Float = 0.8f,
    val enableRAG: Boolean = true,
    val cacheEnabled: Boolean = true
)

/**
 * 决策请求
 */
@Serializable
data class DecisionRequest(
    val goal: String,
    val currentUIState: UIState? = null,
    val history: List<AgentStep> = emptyList(),
    val context: Map<String, String> = emptyMap()
)

/**
 * 决策响应
 */
@Serializable
data class DecisionResponse(
    val strategy: DecisionStrategy,
    val confidence: Float,
    val actions: List<UIAction>,
    val reasoning: String,
    val estimatedDuration: Long,
    val usedRAG: Boolean = false,
    val cacheHit: Boolean = false
)

/**
 * 决策策略
 */
enum class DecisionStrategy {
    LOCAL,    // 本地决策
    CLOUD,    // 云端决策
    HYBRID    // 混合决策
}

/**
 * 混合引擎统计
 */
@Serializable
data class HybridEngineStats(
    val isInitialized: Boolean,
    val runningTaskCount: Int,
    val totalRequests: Long,
    val localDecisions: Long,
    val cloudDecisions: Long,
    val cacheHitRate: Float,
    val averageResponseTime: Long
)
