package com.droidrun.hp.data.entity

import androidx.room.*
import com.droidrun.hp.data.model.TaskStatus
import com.droidrun.hp.data.model.UIAction

/**
 * 任务实体
 */
@Entity(tableName = "tasks")
data class TaskEntity(
    @PrimaryKey
    val id: String,
    val command: String,
    val status: TaskStatus,
    val progress: Float = 0f,
    val currentStep: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val startedAt: Long? = null,
    val completedAt: Long? = null,
    val success: Boolean = false,
    val errorMessage: String? = null,
    val executedActions: List<String> = emptyList(), // JSON字符串列表
    val metadata: Map<String, String> = emptyMap()
)

/**
 * 操作历史实体
 */
@Entity(
    tableName = "action_history",
    foreignKeys = [
        ForeignKey(
            entity = TaskEntity::class,
            parentColumns = ["id"],
            childColumns = ["taskId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("taskId"), Index("timestamp")]
)
data class ActionHistoryEntity(
    @PrimaryKey
    val id: String,
    val taskId: String,
    val actionType: UIAction.Type,
    val targetElementId: String,
    val coordinates: String? = null, // JSON格式的坐标
    val inputText: String? = null,
    val success: Boolean,
    val executionTime: Long,
    val timestamp: Long = System.currentTimeMillis(),
    val errorMessage: String? = null,
    val uiStateBefore: String? = null, // UI状态快照
    val uiStateAfter: String? = null
)

/**
 * UI状态实体
 */
@Entity(
    tableName = "ui_states",
    indices = [Index("timestamp"), Index("appPackage")]
)
data class UIStateEntity(
    @PrimaryKey
    val id: String,
    val appPackage: String,
    val activityName: String,
    val elements: String, // JSON格式的UI元素列表
    val screenWidth: Int,
    val screenHeight: Int,
    val orientation: Int,
    val timestamp: Long = System.currentTimeMillis(),
    val hash: String, // UI状态的哈希值，用于去重
    val isInteractive: Boolean = true
)

/**
 * 缓存实体
 */
@Entity(
    tableName = "cache",
    indices = [Index("key"), Index("expiresAt")]
)
data class CacheEntity(
    @PrimaryKey
    val key: String,
    val value: String, // JSON格式的缓存值
    val type: String, // 缓存类型：decision, llm_response, rag_result等
    val createdAt: Long = System.currentTimeMillis(),
    val expiresAt: Long = Long.MAX_VALUE,
    val accessCount: Long = 0,
    val lastAccessAt: Long = System.currentTimeMillis(),
    val size: Long = 0 // 缓存大小（字节）
)

/**
 * 配置实体
 */
@Entity(tableName = "config")
data class ConfigEntity(
    @PrimaryKey
    val key: String,
    val value: String,
    val type: String, // string, int, boolean, json等
    val category: String = "general", // 配置分类
    val description: String = "",
    val isUserConfigurable: Boolean = true,
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 性能指标实体
 */
@Entity(
    tableName = "performance_metrics",
    indices = [Index("timestamp"), Index("operation")]
)
data class PerformanceMetricEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val operation: String,
    val executionTime: Long,
    val memoryUsage: Long,
    val cpuUsage: Float,
    val networkLatency: Long = 0,
    val cacheHit: Boolean = false,
    val success: Boolean = true,
    val timestamp: Long = System.currentTimeMillis(),
    val threadName: String = "",
    val metadata: Map<String, String> = emptyMap()
)

/**
 * 知识库条目实体（轻量级本地缓存）
 */
@Entity(
    tableName = "knowledge_cache",
    indices = [Index("category"), Index("lastUsed")]
)
data class KnowledgeCacheEntity(
    @PrimaryKey
    val id: String,
    val title: String,
    val content: String,
    val category: String,
    val tags: List<String> = emptyList(),
    val importance: Float = 1.0f,
    val useCount: Long = 0,
    val lastUsed: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis(),
    val source: String = "cloud", // cloud, local, user
    val embedding: String? = null // 向量嵌入（如果有）
)

/**
 * 应用使用统计实体
 */
@Entity(
    tableName = "app_usage_stats",
    indices = [Index("packageName"), Index("date")]
)
data class AppUsageStatsEntity(
    @PrimaryKey
    val id: String,
    val packageName: String,
    val appName: String,
    val date: String, // YYYY-MM-DD格式
    val launchCount: Int = 0,
    val totalUsageTime: Long = 0, // 毫秒
    val automationCount: Int = 0, // 自动化操作次数
    val successRate: Float = 0f,
    val lastLaunch: Long = 0,
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 错误日志实体
 */
@Entity(
    tableName = "error_logs",
    indices = [Index("timestamp"), Index("level")]
)
data class ErrorLogEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val level: String, // ERROR, WARN, INFO
    val tag: String,
    val message: String,
    val stackTrace: String? = null,
    val context: Map<String, String> = emptyMap(),
    val timestamp: Long = System.currentTimeMillis(),
    val deviceInfo: String? = null,
    val appVersion: String? = null
)
