# DroidRun高性能版 - Windows PowerShell构建脚本
# 解决Windows环境下的Gradle构建问题

param(
    [string]$Task = "assembleDebug",
    [switch]$Clean = $false,
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host "DroidRun高性能版构建脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -Task <任务>     指定Gradle任务 (默认: assembleDebug)"
    Write-Host "  -Clean           清理项目"
    Write-Host "  -Help            显示此帮助信息"
    Write-Host ""
    Write-Host "常用任务:" -ForegroundColor Yellow
    Write-Host "  assembleDebug    构建调试版本"
    Write-Host "  assembleRelease  构建发布版本"
    Write-Host "  clean            清理构建文件"
    Write-Host "  test             运行单元测试"
    Write-Host "  connectedAndroidTest  运行设备测试"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1                    # 构建调试版本"
    Write-Host "  .\build.ps1 -Task assembleRelease  # 构建发布版本"
    Write-Host "  .\build.ps1 -Clean             # 清理项目"
    exit 0
}

Write-Host "🚀 DroidRun高性能版构建脚本" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

# 检查Java环境
Write-Host "📋 检查构建环境..." -ForegroundColor Yellow

try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到Java。请安装JDK 11或更高版本。" -ForegroundColor Red
    Write-Host "   下载地址: https://adoptium.net/" -ForegroundColor Yellow
    exit 1
}

# 检查Android SDK
if ($env:ANDROID_HOME) {
    Write-Host "✅ Android SDK: $env:ANDROID_HOME" -ForegroundColor Green
} else {
    Write-Host "⚠️  警告: 未设置ANDROID_HOME环境变量" -ForegroundColor Yellow
    Write-Host "   请安装Android Studio并设置SDK路径" -ForegroundColor Yellow
}

# 检查Gradle Wrapper
$gradlewPath = ".\gradlew.bat"
if (Test-Path $gradlewPath) {
    Write-Host "✅ Gradle Wrapper: 已找到" -ForegroundColor Green
} else {
    Write-Host "❌ 错误: 未找到gradlew.bat" -ForegroundColor Red
    Write-Host "   正在创建Gradle Wrapper..." -ForegroundColor Yellow
    
    # 如果系统安装了Gradle，使用它来生成wrapper
    try {
        gradle wrapper --gradle-version 8.4
        Write-Host "✅ Gradle Wrapper创建成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ 无法创建Gradle Wrapper。请手动安装Gradle。" -ForegroundColor Red
        exit 1
    }
}

# 清理项目（如果需要）
if ($Clean) {
    Write-Host "🧹 清理项目..." -ForegroundColor Yellow
    try {
        & $gradlewPath clean
        Write-Host "✅ 项目清理完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 清理失败: $_" -ForegroundColor Red
        exit 1
    }
}

# 执行构建任务
Write-Host "🔨 执行构建任务: $Task" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan

try {
    $startTime = Get-Date
    
    # 执行Gradle任务
    & $gradlewPath $Task --stacktrace
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "================================================" -ForegroundColor Cyan
        Write-Host "🎉 构建成功完成!" -ForegroundColor Green
        Write-Host "⏱️  构建时间: $($duration.ToString('mm\:ss'))" -ForegroundColor Green
        
        # 显示输出文件位置
        if ($Task -like "*assemble*") {
            Write-Host ""
            Write-Host "📦 输出文件位置:" -ForegroundColor Yellow
            
            $apkPath = "app\build\outputs\apk"
            if (Test-Path $apkPath) {
                Get-ChildItem -Path $apkPath -Recurse -Filter "*.apk" | ForEach-Object {
                    Write-Host "   📱 $($_.FullName)" -ForegroundColor Cyan
                }
            }
            
            Write-Host ""
            Write-Host "🔧 下一步操作:" -ForegroundColor Yellow
            Write-Host "   1. 安装APK: adb install <apk文件路径>" -ForegroundColor White
            Write-Host "   2. 启动应用并配置豆包服务" -ForegroundColor White
            Write-Host "   3. 参考 DOUBAO_SETUP_GUIDE.md 进行配置" -ForegroundColor White
        }
        
    } else {
        Write-Host "================================================" -ForegroundColor Cyan
        Write-Host "❌ 构建失败" -ForegroundColor Red
        Write-Host "💡 常见解决方案:" -ForegroundColor Yellow
        Write-Host "   1. 检查网络连接（下载依赖）" -ForegroundColor White
        Write-Host "   2. 清理项目: .\build.ps1 -Clean" -ForegroundColor White
        Write-Host "   3. 检查Android SDK配置" -ForegroundColor White
        Write-Host "   4. 更新Android Studio和SDK" -ForegroundColor White
        exit 1
    }
    
} catch {
    Write-Host "❌ 构建过程中发生错误: $_" -ForegroundColor Red
    exit 1
}

Write-Host "================================================" -ForegroundColor Cyan
