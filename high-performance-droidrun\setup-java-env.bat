@echo off
echo JDK 17 Environment Setup
echo =========================

echo Step 1: Detecting JDK 17 installation...

REM Check common JDK installation paths
set JDK_FOUND=0

REM Check if JDK 17 is already in PATH
java -version 2>&1 | find "17" >nul
if %errorlevel% equ 0 (
    echo OK: JDK 17 found in PATH
    java -version
    set JDK_FOUND=1
    goto :set_gradle_java
)

REM Check common installation locations
if exist "C:\Program Files\Java\jdk-17*" (
    for /d %%i in ("C:\Program Files\Java\jdk-17*") do (
        echo Found JDK 17 at: %%i
        set JAVA_HOME=%%i
        set JDK_FOUND=1
        goto :set_env
    )
)

if exist "C:\Program Files\Eclipse Adoptium\jdk-17*" (
    for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-17*") do (
        echo Found JDK 17 at: %%i
        set JAVA_HOME=%%i
        set JDK_FOUND=1
        goto :set_env
    )
)

if exist "C:\java\jdk-17*" (
    for /d %%i in ("C:\java\jdk-17*") do (
        echo Found JDK 17 at: %%i
        set JAVA_HOME=%%i
        set JDK_FOUND=1
        goto :set_env
    )
)

if %JDK_FOUND%==0 (
    echo JDK 17 not found. Would you like to download it?
    echo.
    echo Option 1: Download JDK 17 automatically
    echo Option 2: Manual download instructions
    echo Option 3: Skip and use current Java
    echo.
    set /p choice="Choose option (1/2/3): "
    
    if "!choice!"=="1" goto :download_jdk
    if "!choice!"=="2" goto :manual_instructions
    if "!choice!"=="3" goto :use_current_java
    
    echo Invalid choice. Exiting.
    pause
    exit /b 1
)

:download_jdk
echo.
echo Downloading JDK 17...
if not exist "C:\java" mkdir C:\java

echo Downloading Eclipse Temurin JDK 17...
powershell -Command "& {try { Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip' -OutFile 'C:\java\jdk17.zip' -UseBasicParsing; Write-Host 'Download completed' } catch { Write-Host 'Download failed: ' + $_.Exception.Message; exit 1 }}"

if exist "C:\java\jdk17.zip" (
    echo Extracting JDK 17...
    powershell -Command "Expand-Archive -Path 'C:\java\jdk17.zip' -DestinationPath 'C:\java' -Force"
    set JAVA_HOME=C:\java\jdk-17.0.9+9
    echo JDK 17 installed to: %JAVA_HOME%
) else (
    echo Download failed. Please download manually.
    goto :manual_instructions
)

:set_env
echo.
echo Step 2: Setting environment variables...
echo JAVA_HOME will be set to: %JAVA_HOME%

REM Set for current session
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Step 3: Setting permanent environment variables...
echo This requires administrator privileges...

REM Set JAVA_HOME permanently
setx JAVA_HOME "%JAVA_HOME%" /M 2>nul
if %errorlevel% neq 0 (
    echo Warning: Could not set system-wide JAVA_HOME (requires admin)
    echo Setting for current user only...
    setx JAVA_HOME "%JAVA_HOME%"
)

REM Add to PATH permanently
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set SYSTEM_PATH=%%b
echo %SYSTEM_PATH% | find "%JAVA_HOME%\bin" >nul
if %errorlevel% neq 0 (
    setx PATH "%JAVA_HOME%\bin;%SYSTEM_PATH%" /M 2>nul
    if %errorlevel% neq 0 (
        echo Warning: Could not update system PATH (requires admin)
        echo Adding to user PATH...
        for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set USER_PATH=%%b
        if defined USER_PATH (
            setx PATH "%JAVA_HOME%\bin;%USER_PATH%"
        ) else (
            setx PATH "%JAVA_HOME%\bin"
        )
    )
)

:set_gradle_java
echo.
echo Step 4: Configuring Gradle to use JDK 17...

REM Create gradle.properties if it doesn't exist
if not exist "gradle.properties" (
    echo Creating gradle.properties...
    echo # Gradle configuration for DroidRun > gradle.properties
    echo org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m >> gradle.properties
    echo org.gradle.daemon=true >> gradle.properties
    echo org.gradle.parallel=true >> gradle.properties
    echo org.gradle.configureondemand=true >> gradle.properties
    echo android.useAndroidX=true >> gradle.properties
    echo android.enableJetifier=true >> gradle.properties
)

REM Set JAVA_HOME in gradle.properties
echo org.gradle.java.home=%JAVA_HOME% >> gradle.properties

echo.
echo Step 5: Verifying configuration...
echo Current Java version:
"%JAVA_HOME%\bin\java" -version

echo.
echo Environment variables:
echo JAVA_HOME: %JAVA_HOME%
echo PATH includes: %JAVA_HOME%\bin

echo.
echo Step 6: Testing Gradle with JDK 17...
if exist "C:\gradle\gradle-8.4\bin\gradle.bat" (
    echo Testing Gradle...
    C:\gradle\gradle-8.4\bin\gradle --version
    if %errorlevel% equ 0 (
        echo SUCCESS: Gradle is working with JDK 17!
    ) else (
        echo Warning: Gradle test failed
    )
) else (
    echo Gradle not found. Please run install-gradle.bat first.
)

goto :success

:manual_instructions
echo.
echo Manual JDK 17 Installation Instructions:
echo ========================================
echo.
echo 1. Download JDK 17 from:
echo    https://adoptium.net/temurin/releases/?version=17
echo.
echo 2. Install the downloaded file
echo.
echo 3. Note the installation path (usually):
echo    C:\Program Files\Eclipse Adoptium\jdk-17.x.x.x-hotspot
echo.
echo 4. Set environment variables:
echo    JAVA_HOME = [installation path]
echo    PATH = %%JAVA_HOME%%\bin;%%PATH%%
echo.
echo 5. Restart command prompt and run this script again
echo.
pause
exit /b 1

:use_current_java
echo.
echo Using current Java installation...
java -version
echo.
echo Warning: Android development works best with JDK 11 or 17
echo If you encounter issues, please install JDK 17
echo.

:success
echo.
echo ========================================
echo JDK 17 SETUP COMPLETE!
echo ========================================
echo.
echo Configuration summary:
echo   JAVA_HOME: %JAVA_HOME%
echo   Java version: 
"%JAVA_HOME%\bin\java" -version 2>&1 | find "version"
echo.
echo Next steps:
echo   1. Restart your command prompt/PowerShell
echo   2. Run: .\build-with-java17.bat
echo   3. Or run: gradle assembleRelease
echo.
echo Note: You may need to restart your computer for
echo       system-wide environment variables to take effect.
echo.
pause
