@echo off
echo DroidRun Final Build Solution
echo =============================

echo Bypassing Gradle Wrapper completely...
echo Using system Gradle 8.4 directly

echo.
echo Step 1: Setting up environment...

REM Set Gradle path
set GRADLE_HOME=C:\gradle\gradle-8.4
set PATH=%GRADLE_HOME%\bin;%PATH%

REM Check if Gradle is available
"%GRADLE_HOME%\bin\gradle.bat" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Gradle 8.4 not found at %GRADLE_HOME%
    echo Please run: .\install-gradle.bat first
    pause
    exit /b 1
)

echo OK: Gradle 8.4 found

REM Set Java (try multiple locations)
if exist "C:\java\jdk-17.0.9+9" (
    set JAVA_HOME=C:\java\jdk-17.0.9+9
    echo Using downloaded Java 17: %JAVA_HOME%
) else if exist "C:\Program Files\Eclipse Adoptium\jdk-17*" (
    for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-17*") do (
        set JAVA_HOME=%%i
        echo Using system Java 17: %%i
    )
) else (
    echo WARNING: JDK 17 not found, using system Java
    java -version
)

if defined JAVA_HOME (
    set PATH=%JAVA_HOME%\bin;%PATH%
)

REM Set Android SDK
if not defined ANDROID_HOME (
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
        echo Found Android SDK: %ANDROID_HOME%
    ) else (
        echo WARNING: Android SDK not found
        echo Please install Android Studio or set ANDROID_HOME manually
        echo Continuing anyway...
    )
)

echo.
echo Step 2: Environment summary...
echo GRADLE_HOME: %GRADLE_HOME%
echo JAVA_HOME: %JAVA_HOME%
echo ANDROID_HOME: %ANDROID_HOME%

echo.
echo Step 3: Verifying tools...
echo Java version:
if defined JAVA_HOME (
    "%JAVA_HOME%\bin\java" -version
) else (
    java -version
)

echo.
echo Gradle version:
"%GRADLE_HOME%\bin\gradle.bat" --version | find "Gradle"

echo.
echo Step 4: Creating gradle.properties...
REM Create gradle.properties with proper settings
(
echo # Gradle configuration for DroidRun
echo org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
echo org.gradle.daemon=true
echo org.gradle.parallel=true
echo org.gradle.configureondemand=true
echo org.gradle.caching=true
echo android.useAndroidX=true
echo android.enableJetifier=true
echo android.enableR8.fullMode=true
) > gradle.properties

if defined JAVA_HOME (
    echo org.gradle.java.home=%JAVA_HOME% >> gradle.properties
)

echo gradle.properties created

echo.
echo Step 5: Starting build process...
echo ================================

echo Cleaning project...
"%GRADLE_HOME%\bin\gradle.bat" clean --no-daemon

if %errorlevel% neq 0 (
    echo Clean failed, but continuing...
)

echo.
echo Building release APK...
echo This may take several minutes for first build...
"%GRADLE_HOME%\bin\gradle.bat" assembleRelease --no-daemon --stacktrace --info

if %errorlevel% equ 0 (
    echo.
    echo ==========================================
    echo BUILD SUCCESS!
    echo ==========================================
    echo.
    
    REM Find APK file
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo APK created successfully!
        echo Location: app\build\outputs\apk\release\app-release.apk
        
        REM Show file size
        for %%A in ("app\build\outputs\apk\release\app-release.apk") do (
            set /a size_mb=%%~zA/1024/1024
            echo Size: %%~zA bytes (~!size_mb! MB)
        )
        
        echo.
        echo Installation command:
        echo   adb install app\build\outputs\apk\release\app-release.apk
        echo.
        echo Next steps:
        echo   1. Connect Android device via USB
        echo   2. Enable USB debugging in Developer Options
        echo   3. Run: adb install app\build\outputs\apk\release\app-release.apk
        echo   4. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)
        echo   5. Enable accessibility service in Android settings
        
    ) else (
        echo APK not found in expected location
        echo Searching for APK files...
        for /r %%i in (*.apk) do (
            echo Found APK: %%i
        )
    )
    
) else (
    echo.
    echo ==========================================
    echo BUILD FAILED
    echo ==========================================
    echo.
    echo Common solutions:
    echo.
    echo 1. Missing Android SDK:
    echo    - Install Android Studio
    echo    - Set ANDROID_HOME environment variable
    echo.
    echo 2. Network issues:
    echo    - Check internet connection
    echo    - Try again (dependencies may be downloading)
    echo.
    echo 3. Java issues:
    echo    - Run: .\setup-java-env.bat
    echo    - Ensure JDK 11+ is installed
    echo.
    echo 4. Disk space:
    echo    - Ensure at least 5GB free space
    echo.
    echo 5. Antivirus interference:
    echo    - Temporarily disable antivirus
    echo    - Add project folder to exclusions
    echo.
    echo For detailed error analysis, check the output above.
    echo The --stacktrace and --info flags provide detailed information.
)

echo.
echo Build process completed.
pause
