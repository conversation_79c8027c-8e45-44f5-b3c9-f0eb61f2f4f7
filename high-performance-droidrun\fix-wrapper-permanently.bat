@echo off
echo Fixing Gradle Wrapper Permanently
echo ==================================

echo This will replace the broken Gradle Wrapper with working system Gradle

echo.
echo Step 1: Backing up current wrapper files...
if exist "gradlew.bat" (
    copy gradlew.bat gradlew.bat.backup >nul
    echo Backed up gradlew.bat
)
if exist "gradlew" (
    copy gradlew gradlew.backup >nul
    echo Backed up gradlew
)

echo.
echo Step 2: Creating new wrapper that uses system Gradle...

REM Create new gradlew.bat that calls system Gradle
(
echo @echo off
echo REM DroidRun Gradle Wrapper - Fixed Version
echo REM This calls system Gradle instead of wrapper JAR
echo.
echo set GRADLE_HOME=C:\gradle\gradle-8.4
echo set PATH=%%GRADLE_HOME%%\bin;%%PATH%%
echo.
echo if not exist "%%GRADLE_HOME%%\bin\gradle.bat" ^(
echo     echo ERROR: Gradle not found at %%GRADLE_HOME%%
echo     echo Please run install-gradle.bat first
echo     exit /b 1
echo ^)
echo.
echo "%%GRADLE_HOME%%\bin\gradle.bat" %%*
) > gradlew.bat

echo Created new gradlew.bat

REM Create new gradlew for Unix systems
(
echo #!/bin/bash
echo # DroidRun Gradle Wrapper - Fixed Version
echo # This calls system Gradle instead of wrapper JAR
echo.
echo GRADLE_HOME=/c/gradle/gradle-8.4
echo export PATH="$GRADLE_HOME/bin:$PATH"
echo.
echo if [ ! -f "$GRADLE_HOME/bin/gradle" ]; then
echo     echo "ERROR: Gradle not found at $GRADLE_HOME"
echo     echo "Please run install-gradle.bat first"
echo     exit 1
echo fi
echo.
echo "$GRADLE_HOME/bin/gradle" "$@"
) > gradlew

echo Created new gradlew

echo.
echo Step 3: Testing new wrapper...
echo Testing gradlew.bat...
call gradlew.bat --version

if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Gradle wrapper is now working!
    echo.
    echo You can now use:
    echo   .\gradlew.bat assembleRelease
    echo   .\gradlew.bat clean
    echo   .\gradlew.bat build
    echo.
    echo The wrapper now uses system Gradle instead of the problematic JAR file.
) else (
    echo.
    echo FAILED: Wrapper still not working
    echo.
    echo Restoring backup files...
    if exist "gradlew.bat.backup" (
        copy gradlew.bat.backup gradlew.bat >nul
        del gradlew.bat.backup >nul
    )
    if exist "gradlew.backup" (
        copy gradlew.backup gradlew >nul
        del gradlew.backup >nul
    )
    echo.
    echo Please use the direct build method instead:
    echo   .\build-final.bat
)

echo.
pause
