<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- 系统权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    
    <!-- 设备信息权限 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    
    <!-- 性能监控权限 -->
    <uses-permission android:name="android.permission.BATTERY_STATS" />
    <uses-permission android:name="android.permission.DUMP" />
    
    <!-- 硬件特性 -->
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="true" />
    
    <!-- GPU加速支持 -->
    <uses-feature
        android:name="android.hardware.opengles.aep"
        android:required="false" />
    
    <!-- NPU支持（可选） -->
    <uses-feature
        android:name="android.hardware.neuralnetworks"
        android:required="false" />

    <application
        android:name=".DroidRunApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DroidRunHP"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="31">

        <!-- 主Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.DroidRunHP.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 性能监控Activity -->
        <activity
            android:name=".ui.PerformanceMonitorActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity"
            android:theme="@style/Theme.DroidRunHP.NoActionBar" />

        <!-- 任务配置Activity -->
        <activity
            android:name=".ui.TaskConfigActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity"
            android:theme="@style/Theme.DroidRunHP.NoActionBar" />

        <!-- 设置Activity -->
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity"
            android:theme="@style/Theme.DroidRunHP.NoActionBar" />

        <!-- DroidRun核心服务 -->
        <service
            android:name=".service.DroidRunCoreService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataProcessing" />

        <!-- 高性能无障碍服务 -->
        <service
            android:name=".service.HighPerformanceAccessibilityService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- 系统集成服务 -->
        <service
            android:name=".service.SystemIntegrationService"
            android:enabled="true"
            android:exported="false" />

        <!-- 启动时自动启动的接收器 -->
        <receiver
            android:name=".receiver.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- 任务状态广播接收器 -->
        <receiver
            android:name=".receiver.TaskStatusReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.droidrun.hp.TASK_COMPLETE" />
                <action android:name="com.droidrun.hp.TASK_CANCELLED" />
                <action android:name="com.droidrun.hp.TASK_ERROR" />
            </intent-filter>
        </receiver>

        <!-- 文件提供器 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 网络安全配置 -->
        <meta-data
            android:name="android.net.http.network_security_config"
            android:resource="@xml/network_security_config" />

        <!-- 性能优化配置 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        
        <meta-data
            android:name="android.allow_multiple_resumed_activities"
            android:value="true" />

    </application>

    <!-- 查询其他应用的权限 -->
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
        <intent>
            <action android:name="android.settings.ACCESSIBILITY_SETTINGS" />
        </intent>
        <intent>
            <action android:name="android.settings.APPLICATION_DETAILS_SETTINGS" />
        </intent>
    </queries>

</manifest>
