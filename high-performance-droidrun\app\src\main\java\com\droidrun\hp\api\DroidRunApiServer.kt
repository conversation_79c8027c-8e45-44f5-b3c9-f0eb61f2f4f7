package com.droidrun.hp.api

import android.content.Context
import com.droidrun.hp.data.model.*
import com.droidrun.hp.data.repository.TaskRepository
import com.droidrun.hp.service.DroidRunCoreService
import com.droidrun.hp.service.HighPerformanceAccessibilityService
import com.droidrun.hp.utils.PerformanceProfiler
import fi.iki.elonen.NanoHTTPD
import kotlinx.coroutines.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DroidRun API服务器
 * 提供REST API接口，保持与原CLI工具的兼容性
 */
@Singleton
class DroidRunApiServer @Inject constructor(
    private val context: Context,
    private val taskRepository: TaskRepository
) : NanoHTTPD(8080) {
    
    companion object {
        private const val API_VERSION = "v1"
        private const val CORS_HEADERS = "Content-Type, Authorization, X-Requested-With"
    }
    
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    private val serverScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var isRunning = false
    
    /**
     * 启动API服务器
     */
    fun startServer(): Boolean {
        return try {
            start(NanoHTTPD.SOCKET_READ_TIMEOUT, false)
            isRunning = true
            Timber.i("DroidRun API服务器已启动，端口: 8080")
            true
        } catch (e: Exception) {
            Timber.e(e, "启动API服务器失败")
            false
        }
    }
    
    /**
     * 停止API服务器
     */
    fun stopServer() {
        if (isRunning) {
            stop()
            serverScope.cancel()
            isRunning = false
            Timber.i("DroidRun API服务器已停止")
        }
    }
    
    /**
     * 处理HTTP请求
     */
    override fun serve(session: IHTTPSession): Response {
        return PerformanceProfiler.measureExecutionTime("ApiServer.Serve") {
            try {
                // 添加CORS头
                val response = when (session.method) {
                    Method.OPTIONS -> handleOptions()
                    Method.GET -> handleGet(session)
                    Method.POST -> handlePost(session)
                    Method.PUT -> handlePut(session)
                    Method.DELETE -> handleDelete(session)
                    else -> createErrorResponse(405, "Method Not Allowed")
                }
                
                addCorsHeaders(response)
                response
                
            } catch (e: Exception) {
                Timber.e(e, "API请求处理异常: ${session.uri}")
                createErrorResponse(500, "Internal Server Error: ${e.message}")
            }
        }
    }
    
    /**
     * 处理OPTIONS请求（CORS预检）
     */
    private fun handleOptions(): Response {
        return newFixedLengthResponse(Response.Status.OK, MIME_PLAINTEXT, "")
    }
    
    /**
     * 处理GET请求
     */
    private fun handleGet(session: IHTTPSession): Response {
        val uri = session.uri
        
        return when {
            uri == "/api/$API_VERSION/status" -> handleGetStatus()
            uri == "/api/$API_VERSION/tasks" -> handleGetTasks(session)
            uri.startsWith("/api/$API_VERSION/tasks/") -> handleGetTask(session)
            uri == "/api/$API_VERSION/ui/state" -> handleGetUIState()
            uri == "/api/$API_VERSION/performance" -> handleGetPerformance()
            uri == "/api/$API_VERSION/health" -> handleHealthCheck()
            uri == "/" || uri == "/api" -> handleApiInfo()
            else -> createErrorResponse(404, "Endpoint not found")
        }
    }
    
    /**
     * 处理POST请求
     */
    private fun handlePost(session: IHTTPSession): Response {
        val uri = session.uri
        
        return when {
            uri == "/api/$API_VERSION/tasks" -> handleCreateTask(session)
            uri == "/api/$API_VERSION/tasks/execute" -> handleExecuteTask(session)
            uri.startsWith("/api/$API_VERSION/tasks/") && uri.endsWith("/cancel") -> handleCancelTask(session)
            uri == "/api/$API_VERSION/ui/action" -> handleUIAction(session)
            else -> createErrorResponse(404, "Endpoint not found")
        }
    }
    
    /**
     * 处理PUT请求
     */
    private fun handlePut(session: IHTTPSession): Response {
        val uri = session.uri
        
        return when {
            uri.startsWith("/api/$API_VERSION/tasks/") -> handleUpdateTask(session)
            else -> createErrorResponse(404, "Endpoint not found")
        }
    }
    
    /**
     * 处理DELETE请求
     */
    private fun handleDelete(session: IHTTPSession): Response {
        val uri = session.uri
        
        return when {
            uri.startsWith("/api/$API_VERSION/tasks/") -> handleDeleteTask(session)
            else -> createErrorResponse(404, "Endpoint not found")
        }
    }
    
    /**
     * 获取服务状态
     */
    private fun handleGetStatus(): Response {
        return runBlocking {
            try {
                val accessibilityService = HighPerformanceAccessibilityService.getInstance()
                val isAccessibilityEnabled = accessibilityService != null
                
                val status = mapOf(
                    "status" to "running",
                    "version" to "1.0.0",
                    "api_version" to API_VERSION,
                    "accessibility_service" to isAccessibilityEnabled,
                    "timestamp" to System.currentTimeMillis()
                )
                
                createJsonResponse(status)
            } catch (e: Exception) {
                createErrorResponse(500, "Failed to get status: ${e.message}")
            }
        }
    }
    
    /**
     * 获取任务列表
     */
    private fun handleGetTasks(session: IHTTPSession): Response {
        return runBlocking {
            try {
                val params = session.parms
                val status = params["status"]
                val limit = params["limit"]?.toIntOrNull() ?: 50
                
                val tasks = if (status != null) {
                    // 根据状态过滤
                    val taskStatus = TaskStatus.valueOf(status.uppercase())
                    // 这里需要实现按状态查询的方法
                    taskRepository.getRecentTasks(limit)
                } else {
                    taskRepository.getRecentTasks(limit)
                }
                
                val response = mapOf(
                    "tasks" to tasks,
                    "total" to tasks.size,
                    "timestamp" to System.currentTimeMillis()
                )
                
                createJsonResponse(response)
            } catch (e: Exception) {
                createErrorResponse(500, "Failed to get tasks: ${e.message}")
            }
        }
    }
    
    /**
     * 获取单个任务
     */
    private fun handleGetTask(session: IHTTPSession): Response {
        return runBlocking {
            try {
                val taskId = extractTaskId(session.uri)
                if (taskId == null) {
                    return@runBlocking createErrorResponse(400, "Invalid task ID")
                }
                
                val task = taskRepository.getTaskById(taskId)
                if (task == null) {
                    return@runBlocking createErrorResponse(404, "Task not found")
                }
                
                // 获取任务的操作历史
                val actionHistory = taskRepository.getTaskActionHistory(taskId)
                
                val response = mapOf(
                    "task" to task,
                    "action_history" to actionHistory,
                    "timestamp" to System.currentTimeMillis()
                )
                
                createJsonResponse(response)
            } catch (e: Exception) {
                createErrorResponse(500, "Failed to get task: ${e.message}")
            }
        }
    }
    
    /**
     * 创建任务
     */
    private fun handleCreateTask(session: IHTTPSession): Response {
        return runBlocking {
            try {
                val requestBody = getRequestBody(session)
                val request = json.decodeFromString<CreateTaskRequest>(requestBody)
                
                val taskId = UUID.randomUUID().toString()
                val taskInfo = TaskInfo(
                    id = taskId,
                    command = request.command,
                    status = TaskStatus.PENDING
                )
                
                val success = taskRepository.createTask(taskInfo)
                if (!success) {
                    return@runBlocking createErrorResponse(500, "Failed to create task")
                }
                
                val response = mapOf(
                    "task_id" to taskId,
                    "status" to "created",
                    "timestamp" to System.currentTimeMillis()
                )
                
                createJsonResponse(response, Response.Status.CREATED)
            } catch (e: Exception) {
                createErrorResponse(400, "Invalid request: ${e.message}")
            }
        }
    }
    
    /**
     * 执行任务
     */
    private fun handleExecuteTask(session: IHTTPSession): Response {
        return runBlocking {
            try {
                val requestBody = getRequestBody(session)
                val request = json.decodeFromString<ExecuteTaskRequest>(requestBody)
                
                val taskId = request.taskId ?: UUID.randomUUID().toString()
                
                // 创建任务
                val taskInfo = TaskInfo(
                    id = taskId,
                    command = request.command,
                    status = TaskStatus.PENDING
                )
                taskRepository.createTask(taskInfo)
                
                // 启动任务执行
                val intent = android.content.Intent(context, DroidRunCoreService::class.java).apply {
                    action = DroidRunCoreService.ACTION_START_TASK
                    putExtra(DroidRunCoreService.EXTRA_TASK_ID, taskId)
                    putExtra(DroidRunCoreService.EXTRA_TASK_COMMAND, request.command)
                }
                context.startForegroundService(intent)
                
                val response = mapOf(
                    "task_id" to taskId,
                    "status" to "executing",
                    "command" to request.command,
                    "timestamp" to System.currentTimeMillis()
                )
                
                createJsonResponse(response)
            } catch (e: Exception) {
                createErrorResponse(400, "Failed to execute task: ${e.message}")
            }
        }
    }
    
    /**
     * 取消任务
     */
    private fun handleCancelTask(session: IHTTPSession): Response {
        return runBlocking {
            try {
                val taskId = extractTaskId(session.uri)
                if (taskId == null) {
                    return@runBlocking createErrorResponse(400, "Invalid task ID")
                }
                
                // 发送取消任务的Intent
                val intent = android.content.Intent(context, DroidRunCoreService::class.java).apply {
                    action = DroidRunCoreService.ACTION_STOP_TASK
                    putExtra(DroidRunCoreService.EXTRA_TASK_ID, taskId)
                }
                context.startService(intent)
                
                // 更新任务状态
                taskRepository.updateTaskStatus(taskId, TaskStatus.CANCELLED)
                
                val response = mapOf(
                    "task_id" to taskId,
                    "status" to "cancelled",
                    "timestamp" to System.currentTimeMillis()
                )
                
                createJsonResponse(response)
            } catch (e: Exception) {
                createErrorResponse(500, "Failed to cancel task: ${e.message}")
            }
        }
    }
    
    /**
     * 获取UI状态
     */
    private fun handleGetUIState(): Response {
        return try {
            val accessibilityService = HighPerformanceAccessibilityService.getInstance()
            val uiState = accessibilityService?.getCurrentUIState()
            
            if (uiState != null) {
                val response = mapOf(
                    "ui_state" to uiState,
                    "timestamp" to System.currentTimeMillis()
                )
                createJsonResponse(response)
            } else {
                createErrorResponse(503, "Accessibility service not available")
            }
        } catch (e: Exception) {
            createErrorResponse(500, "Failed to get UI state: ${e.message}")
        }
    }
    
    /**
     * 健康检查
     */
    private fun handleHealthCheck(): Response {
        val health = mapOf(
            "status" to "healthy",
            "timestamp" to System.currentTimeMillis(),
            "uptime" to System.currentTimeMillis() // 简化实现
        )
        return createJsonResponse(health)
    }
    
    /**
     * API信息
     */
    private fun handleApiInfo(): Response {
        val info = mapOf(
            "name" to "DroidRun API",
            "version" to API_VERSION,
            "description" to "DroidRun高性能Android自动化API",
            "endpoints" to listOf(
                "/api/v1/status",
                "/api/v1/tasks",
                "/api/v1/tasks/execute",
                "/api/v1/ui/state",
                "/api/v1/health"
            )
        )
        return createJsonResponse(info)
    }
    
    // 辅助方法
    private fun createJsonResponse(data: Any, status: Response.Status = Response.Status.OK): Response {
        val jsonString = json.encodeToString(data)
        return newFixedLengthResponse(status, "application/json", jsonString)
    }
    
    private fun createErrorResponse(code: Int, message: String): Response {
        val error = mapOf(
            "error" to true,
            "code" to code,
            "message" to message,
            "timestamp" to System.currentTimeMillis()
        )
        val status = when (code) {
            400 -> Response.Status.BAD_REQUEST
            404 -> Response.Status.NOT_FOUND
            405 -> Response.Status.METHOD_NOT_ALLOWED
            500 -> Response.Status.INTERNAL_ERROR
            503 -> Response.Status.SERVICE_UNAVAILABLE
            else -> Response.Status.INTERNAL_ERROR
        }
        return createJsonResponse(error, status)
    }
    
    private fun addCorsHeaders(response: Response): Response {
        response.addHeader("Access-Control-Allow-Origin", "*")
        response.addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        response.addHeader("Access-Control-Allow-Headers", CORS_HEADERS)
        return response
    }
    
    private fun getRequestBody(session: IHTTPSession): String {
        val files = mutableMapOf<String, String>()
        session.parseBody(files)
        return files["postData"] ?: ""
    }
    
    private fun extractTaskId(uri: String): String? {
        val parts = uri.split("/")
        val taskIndex = parts.indexOf("tasks")
        return if (taskIndex >= 0 && taskIndex + 1 < parts.size) {
            parts[taskIndex + 1].split("?")[0] // 移除查询参数
        } else null
    }
}

/**
 * 创建任务请求
 */
@kotlinx.serialization.Serializable
data class CreateTaskRequest(
    val command: String,
    val priority: Int = 0,
    val metadata: Map<String, String> = emptyMap()
)

/**
 * 执行任务请求
 */
@kotlinx.serialization.Serializable
data class ExecuteTaskRequest(
    val command: String,
    val taskId: String? = null,
    val async: Boolean = true,
    val timeout: Long = 30000L
)
