# DroidRun Java Fix and Build Script
Write-Host "DroidRun Java Fix and Build Solution" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

Write-Host "Problem detected: Java 8 is incompatible with Gradle 8.4" -ForegroundColor Red
Write-Host "Solution: Download and use Java 17 automatically" -ForegroundColor Yellow

Write-Host ""
Write-Host "Step 1: Downloading Java 17..." -ForegroundColor Yellow

# Create java directory
$javaDir = "C:\java"
if (-not (Test-Path $javaDir)) {
    New-Item -ItemType Directory -Path $javaDir -Force | Out-Null
}

# Download Java 17 if not exists
$jdk17Path = "C:\java\jdk-17.0.9+9"
if (-not (Test-Path $jdk17Path)) {
    Write-Host "Downloading OpenJDK 17..." -ForegroundColor Cyan
    $downloadUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip"
    $zipPath = "C:\java\jdk17.zip"
    
    try {
        Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing
        Write-Host "Download completed" -ForegroundColor Green
        
        Write-Host "Extracting Java 17..." -ForegroundColor Cyan
        Expand-Archive -Path $zipPath -DestinationPath $javaDir -Force
        Remove-Item $zipPath -Force
        Write-Host "Java 17 extracted successfully" -ForegroundColor Green
        
    } catch {
        Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please download Java 17 manually from: https://adoptium.net/" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "Java 17 already exists at: $jdk17Path" -ForegroundColor Green
}

Write-Host ""
Write-Host "Step 2: Setting up environment with Java 17..." -ForegroundColor Yellow

# Set Java 17 environment
$env:JAVA_HOME = $jdk17Path
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

# Set Gradle environment
$env:GRADLE_HOME = "C:\gradle\gradle-8.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"

# Verify Java 17
Write-Host "Verifying Java 17 installation:" -ForegroundColor Cyan
try {
    & "$env:JAVA_HOME\bin\java.exe" -version
    Write-Host "Java 17 is working!" -ForegroundColor Green
} catch {
    Write-Host "Java 17 verification failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Step 3: Creating correct gradle.properties..." -ForegroundColor Yellow

# Create gradle.properties with correct path format
$gradleProperties = @"
# Gradle configuration for DroidRun
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
org.gradle.java.home=$($env:JAVA_HOME -replace '\\', '/')
"@

$gradleProperties | Out-File -FilePath "gradle.properties" -Encoding UTF8
Write-Host "gradle.properties created with Java 17 path" -ForegroundColor Green

Write-Host ""
Write-Host "Step 4: Setting up Android SDK..." -ForegroundColor Yellow

# Try to find Android SDK
$androidSdkPaths = @(
    "$env:USERPROFILE\AppData\Local\Android\Sdk",
    "C:\Android\Sdk",
    "C:\Users\<USER>\AppData\Local\Android\Sdk"
)

$androidSdkFound = $false
foreach ($path in $androidSdkPaths) {
    if (Test-Path $path) {
        $env:ANDROID_HOME = $path
        Write-Host "Found Android SDK: $path" -ForegroundColor Green
        $androidSdkFound = $true
        break
    }
}

if (-not $androidSdkFound) {
    Write-Host "Android SDK not found. Installing Android command line tools..." -ForegroundColor Yellow
    
    # Download Android command line tools
    $cmdlineToolsUrl = "https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip"
    $androidDir = "C:\Android"
    $cmdlineToolsZip = "$androidDir\cmdline-tools.zip"
    
    if (-not (Test-Path $androidDir)) {
        New-Item -ItemType Directory -Path $androidDir -Force | Out-Null
    }
    
    try {
        Write-Host "Downloading Android command line tools..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $cmdlineToolsUrl -OutFile $cmdlineToolsZip -UseBasicParsing
        
        Write-Host "Extracting Android tools..." -ForegroundColor Cyan
        Expand-Archive -Path $cmdlineToolsZip -DestinationPath $androidDir -Force
        Remove-Item $cmdlineToolsZip -Force
        
        # Set up Android SDK
        $env:ANDROID_HOME = "$androidDir\Sdk"
        $env:PATH = "$env:ANDROID_HOME\cmdline-tools\latest\bin;$env:ANDROID_HOME\platform-tools;$env:PATH"
        
        Write-Host "Android SDK setup completed" -ForegroundColor Green
        
    } catch {
        Write-Host "Android SDK download failed, but continuing..." -ForegroundColor Yellow
        Write-Host "You may need to install Android Studio manually" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Step 5: Environment summary..." -ForegroundColor Yellow
Write-Host "JAVA_HOME: $env:JAVA_HOME"
Write-Host "GRADLE_HOME: $env:GRADLE_HOME"
Write-Host "ANDROID_HOME: $env:ANDROID_HOME"

Write-Host ""
Write-Host "Step 6: Starting build with Java 17..." -ForegroundColor Yellow
Write-Host "======================================" -ForegroundColor Yellow

$gradlePath = "$env:GRADLE_HOME\bin\gradle.bat"

Write-Host "Cleaning project..." -ForegroundColor Cyan
try {
    & $gradlePath clean --no-daemon
    Write-Host "Clean completed" -ForegroundColor Green
} catch {
    Write-Host "Clean failed, but continuing..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Building release APK..." -ForegroundColor Cyan
Write-Host "This may take 5-10 minutes for first build..." -ForegroundColor Yellow

try {
    & $gradlePath assembleRelease --no-daemon --info
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "==========================================" -ForegroundColor Green
        Write-Host "BUILD SUCCESS!" -ForegroundColor Green
        Write-Host "==========================================" -ForegroundColor Green
        Write-Host ""
        
        # Find APK
        $apkPath = "app\build\outputs\apk\release\app-release.apk"
        if (Test-Path $apkPath) {
            $apkFile = Get-Item $apkPath
            $sizeMB = [math]::Round($apkFile.Length / 1MB, 2)
            
            Write-Host "🎉 APK created successfully!" -ForegroundColor Green
            Write-Host "📍 Location: $apkPath" -ForegroundColor Cyan
            Write-Host "📦 Size: $($apkFile.Length) bytes (~$sizeMB MB)" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "📱 Installation command:" -ForegroundColor Yellow
            Write-Host "   adb install $apkPath" -ForegroundColor White
            Write-Host ""
            Write-Host "🔧 Next steps:" -ForegroundColor Yellow
            Write-Host "   1. Connect Android device via USB" -ForegroundColor White
            Write-Host "   2. Enable USB debugging" -ForegroundColor White
            Write-Host "   3. Run: adb install $apkPath" -ForegroundColor White
            Write-Host "   4. Configure Doubao services (see DOUBAO_SETUP_GUIDE.md)" -ForegroundColor White
            Write-Host "   5. Enable accessibility service" -ForegroundColor White
            
        } else {
            Write-Host "APK not found, searching..." -ForegroundColor Yellow
            Get-ChildItem -Recurse -Filter "*.apk" | ForEach-Object {
                Write-Host "Found: $($_.FullName)" -ForegroundColor Cyan
            }
        }
        
    } else {
        Write-Host ""
        Write-Host "==========================================" -ForegroundColor Red
        Write-Host "BUILD FAILED" -ForegroundColor Red
        Write-Host "==========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "The build failed even with Java 17." -ForegroundColor Yellow
        Write-Host "This is likely due to missing Android SDK." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Recommended solution:" -ForegroundColor Yellow
        Write-Host "1. Download and install Android Studio" -ForegroundColor White
        Write-Host "2. Open Android Studio and install Android SDK" -ForegroundColor White
        Write-Host "3. Set ANDROID_HOME environment variable" -ForegroundColor White
        Write-Host "4. Try building again" -ForegroundColor White
    }
    
} catch {
    Write-Host "Build failed with exception: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Build process completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
